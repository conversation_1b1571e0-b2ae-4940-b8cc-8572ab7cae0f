# AWS API Gateway Terraform Master Guide

## Table of Contents
1. [Core API Gateway Concepts](#core-api-gateway-concepts)
2. [REST API Terraform Resources](#rest-api-terraform-resources)
3. [HTTP API (v2) Terraform Resources](#http-api-v2-terraform-resources)
4. [WebSocket API Terraform Resources](#websocket-api-terraform-resources)
5. [Authentication & Authorization](#authentication--authorization)
6. [Integration Patterns](#integration-patterns)
7. [Advanced Features](#advanced-features)
8. [Deployment Strategies](#deployment-strategies)
9. [Monitoring & Logging](#monitoring--logging)
10. [Production Examples](#production-examples)

## Core API Gateway Concepts

### API Gateway Types Comparison

| Feature | REST API | HTTP API | WebSocket API |
|---------|----------|----------|---------------|
| **Protocol** | HTTP/HTTPS | HTTP/HTTPS | WebSocket |
| **Pricing** | Higher | Lower (up to 70% cheaper) | Per message + connection |
| **Performance** | Good | Better (up to 60% faster) | Real-time bidirectional |
| **Use Cases** | Full-featured APIs | Simple HTTP APIs | Real-time apps, chat, gaming |

### When to Choose Each Type

#### REST API
- **Use when you need:**
  - API keys and usage plans
  - Request validation
  - Caching
  - AWS WAF integration
  - Private endpoints
  - Per-client throttling
  - Custom gateway responses
  - Request/response transformations

#### HTTP API (v2)
- **Use when you need:**
  - Lower cost and higher performance
  - Simple HTTP APIs
  - JWT authorization
  - OIDC integration
  - Automatic deployments
  - Private integrations with ALB/Cloud Map

#### WebSocket API
- **Use when you need:**
  - Real-time bidirectional communication
  - Chat applications
  - Live updates/notifications
  - Gaming applications
  - Financial trading platforms
  - Collaboration tools

### Core Components

#### REST API Components
1. **API Gateway REST API** - The container for all resources
2. **Resources** - URL paths (e.g., /users, /users/{id})
3. **Methods** - HTTP verbs (GET, POST, PUT, DELETE)
4. **Integrations** - Backend connections (Lambda, HTTP, AWS services)
5. **Deployments** - Snapshots of API configuration
6. **Stages** - Named references to deployments (dev, staging, prod)

#### HTTP API Components
1. **API Gateway v2 API** - The container for routes
2. **Routes** - Combination of HTTP method and path
3. **Integrations** - Backend connections
4. **Stages** - Environment configurations with auto-deploy

#### WebSocket API Components
1. **API Gateway v2 API** - WebSocket protocol container
2. **Routes** - Message routing based on route keys
3. **Integrations** - Backend message handlers
4. **Stages** - Environment configurations
5. **Route Selection Expression** - Determines message routing

### Key Differences in Terraform Resources

| Component | REST API | HTTP/WebSocket API |
|-----------|----------|-------------------|
| API | `aws_api_gateway_rest_api` | `aws_apigatewayv2_api` |
| Routes/Resources | `aws_api_gateway_resource` | `aws_apigatewayv2_route` |
| Methods | `aws_api_gateway_method` | Built into route |
| Integrations | `aws_api_gateway_integration` | `aws_apigatewayv2_integration` |
| Deployments | `aws_api_gateway_deployment` | `aws_apigatewayv2_deployment` |
| Stages | `aws_api_gateway_stage` | `aws_apigatewayv2_stage` |

## REST API Terraform Resources

### Core Resources Overview

#### aws_api_gateway_rest_api
The foundation resource that creates the API Gateway REST API.

```hcl
resource "aws_api_gateway_rest_api" "example" {
  name        = "example-api"
  description = "Example REST API"
  
  endpoint_configuration {
    types = ["REGIONAL"]  # EDGE, REGIONAL, PRIVATE
  }
  
  # OpenAPI specification (optional)
  body = jsonencode({
    openapi = "3.0.1"
    info = {
      title   = "example"
      version = "1.0"
    }
    # ... OpenAPI spec
  })
  
  # Policy for private APIs
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = "*"
        Action = "execute-api:Invoke"
        Resource = "*"
      }
    ]
  })
  
  tags = {
    Environment = "production"
  }
}
```

#### aws_api_gateway_resource
Defines URL path segments for the API.

```hcl
resource "aws_api_gateway_resource" "users" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  parent_id   = aws_api_gateway_rest_api.example.root_resource_id
  path_part   = "users"
}

resource "aws_api_gateway_resource" "user_id" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  parent_id   = aws_api_gateway_resource.users.id
  path_part   = "{id}"
}
```

#### aws_api_gateway_method
Defines HTTP methods for resources.

```hcl
resource "aws_api_gateway_method" "users_get" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "GET"
  authorization = "AWS_IAM"  # NONE, AWS_IAM, CUSTOM, COGNITO_USER_POOLS
  
  # API Key required
  api_key_required = true
  
  # Request validation
  request_validator_id = aws_api_gateway_request_validator.example.id
  
  # Request parameters
  request_parameters = {
    "method.request.querystring.limit" = false
    "method.request.header.x-api-key"  = true
  }
  
  # Request models
  request_models = {
    "application/json" = aws_api_gateway_model.user.name
  }
}
```

#### aws_api_gateway_integration
Connects methods to backend services.

```hcl
resource "aws_api_gateway_integration" "lambda" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_get.http_method

  integration_http_method = "POST"  # Always POST for Lambda
  type                   = "AWS_PROXY"  # AWS, AWS_PROXY, HTTP, HTTP_PROXY, MOCK
  uri                    = aws_lambda_function.example.invoke_arn

  # For non-proxy integrations
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }

  # Request parameter mapping
  request_parameters = {
    "integration.request.header.X-Authorization" = "method.request.header.Authorization"
  }
}

# HTTP Integration Example
resource "aws_api_gateway_integration" "http" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method

  type                    = "HTTP"
  integration_http_method = "POST"
  uri                     = "https://backend.example.com/api/users"

  connection_type = "VPC_LINK"  # INTERNET, VPC_LINK
  connection_id   = aws_api_gateway_vpc_link.example.id
}
```

#### aws_api_gateway_method_response
Defines response structure for methods.

```hcl
resource "aws_api_gateway_method_response" "response_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_get.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }

  response_models = {
    "application/json" = aws_api_gateway_model.user_list.name
  }
}
```

#### aws_api_gateway_integration_response
Maps integration responses to method responses.

```hcl
resource "aws_api_gateway_integration_response" "response_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_get.http_method
  status_code = aws_api_gateway_method_response.response_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  response_templates = {
    "application/json" = jsonencode({
      message = "Success"
      data    = "$input.json('$')"
    })
  }
}
```

#### aws_api_gateway_deployment
Creates a snapshot of the API configuration.

```hcl
resource "aws_api_gateway_deployment" "example" {
  depends_on = [
    aws_api_gateway_method.users_get,
    aws_api_gateway_integration.lambda,
  ]

  rest_api_id = aws_api_gateway_rest_api.example.id

  # Force new deployment on changes
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.users.id,
      aws_api_gateway_method.users_get.id,
      aws_api_gateway_integration.lambda.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}
```

#### aws_api_gateway_stage
Creates named environments for deployments.

```hcl
resource "aws_api_gateway_stage" "prod" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "prod"

  # Caching
  cache_cluster_enabled = true
  cache_cluster_size    = "0.5"

  # Throttling
  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  # Access logging
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gw.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      caller         = "$context.identity.caller"
      user           = "$context.identity.user"
      requestTime    = "$context.requestTime"
      httpMethod     = "$context.httpMethod"
      resourcePath   = "$context.resourcePath"
      status         = "$context.status"
      protocol       = "$context.protocol"
      responseLength = "$context.responseLength"
    })
  }

  # X-Ray tracing
  xray_tracing_enabled = true

  tags = {
    Environment = "production"
  }
}
```

## HTTP API (v2) Terraform Resources

### Core Resources Overview

#### aws_apigatewayv2_api
Creates HTTP or WebSocket APIs.

```hcl
resource "aws_apigatewayv2_api" "example" {
  name          = "example-http-api"
  description   = "Example HTTP API"
  protocol_type = "HTTP"  # HTTP or WEBSOCKET

  # CORS configuration
  cors_configuration {
    allow_credentials = false
    allow_headers     = ["content-type", "x-amz-date", "authorization"]
    allow_methods     = ["*"]
    allow_origins     = ["*"]
    expose_headers    = ["date", "keep-alive"]
    max_age          = 86400
  }

  # Disable execute API endpoint
  disable_execute_api_endpoint = false

  tags = {
    Environment = "production"
  }
}
```

#### aws_apigatewayv2_route
Defines routes for HTTP APIs (combines path and method).

```hcl
resource "aws_apigatewayv2_route" "get_users" {
  api_id    = aws_apigatewayv2_api.example.id
  route_key = "GET /users"

  # Authorization
  authorization_type = "JWT"  # NONE, AWS_IAM, JWT, CUSTOM
  authorizer_id      = aws_apigatewayv2_authorizer.jwt.id

  # Integration
  target = "integrations/${aws_apigatewayv2_integration.lambda.id}"

  # Request parameters
  request_parameter {
    location        = "querystring"
    name           = "limit"
    required       = false
  }
}

# Catch-all route
resource "aws_apigatewayv2_route" "proxy" {
  api_id    = aws_apigatewayv2_api.example.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}
```

#### aws_apigatewayv2_integration
Connects routes to backend services.

```hcl
resource "aws_apigatewayv2_integration" "lambda" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "AWS_PROXY"  # AWS_PROXY, HTTP_PROXY, AWS, HTTP, MOCK

  connection_type      = "INTERNET"  # INTERNET, VPC_LINK
  description          = "Lambda integration"
  integration_method   = "POST"
  integration_uri      = aws_lambda_function.example.invoke_arn
  passthrough_behavior = "WHEN_NO_MATCH"  # WHEN_NO_MATCH, WHEN_NO_TEMPLATES, NEVER

  # Request transformation
  request_parameters = {
    "overwrite:header.x-forwarded-for" = "$request.header.x-forwarded-for"
  }

  # Response transformation
  response_parameters {
    status_code = "200"
    mappings = {
      "overwrite:header.access-control-allow-origin" = "'*'"
    }
  }
}

# HTTP Integration with VPC Link
resource "aws_apigatewayv2_integration" "http_vpc" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "HTTP_PROXY"

  connection_type = "VPC_LINK"
  connection_id   = aws_apigatewayv2_vpc_link.example.id

  integration_method = "ANY"
  integration_uri    = "http://internal-alb.example.com/{proxy}"
}
```

#### aws_apigatewayv2_stage
Creates deployment stages for HTTP APIs.

```hcl
resource "aws_apigatewayv2_stage" "prod" {
  api_id      = aws_apigatewayv2_api.example.id
  name        = "prod"
  auto_deploy = true  # Automatic deployment on changes

  # Throttling
  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  # Access logging
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gw.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      requestTime    = "$context.requestTime"
      httpMethod     = "$context.httpMethod"
      routeKey       = "$context.routeKey"
      status         = "$context.status"
      protocol       = "$context.protocol"
      responseLength = "$context.responseLength"
      error          = "$context.error.message"
      integrationError = "$context.integrationErrorMessage"
    })
  }

  tags = {
    Environment = "production"
  }
}
```

#### aws_apigatewayv2_authorizer
Configures authorization for HTTP APIs.

```hcl
# JWT Authorizer
resource "aws_apigatewayv2_authorizer" "jwt" {
  api_id           = aws_apigatewayv2_api.example.id
  authorizer_type  = "JWT"
  identity_sources = ["$request.header.Authorization"]
  name             = "jwt-authorizer"

  jwt_configuration {
    audience = ["api.example.com"]
    issuer   = "https://cognito-idp.us-west-2.amazonaws.com/us-west-2_example"
  }
}

# Lambda Authorizer
resource "aws_apigatewayv2_authorizer" "lambda" {
  api_id                            = aws_apigatewayv2_api.example.id
  authorizer_type                   = "REQUEST"
  authorizer_uri                    = aws_lambda_function.auth.invoke_arn
  identity_sources                  = ["$request.header.Authorization"]
  name                             = "lambda-authorizer"
  authorizer_payload_format_version = "2.0"
  authorizer_result_ttl_in_seconds  = 300
  enable_simple_responses           = true
}
```

## WebSocket API Terraform Resources

### Core Concepts
WebSocket APIs enable real-time, bidirectional communication between clients and servers. Key concepts include:

- **Route Selection Expression**: Determines how incoming messages are routed
- **Connection Management**: Handling client connections and disconnections
- **Message Routing**: Directing messages to appropriate backend handlers

#### aws_apigatewayv2_api (WebSocket)
Creates a WebSocket API.

```hcl
resource "aws_apigatewayv2_api" "websocket" {
  name                       = "example-websocket-api"
  protocol_type             = "WEBSOCKET"
  route_selection_expression = "$request.body.action"

  description = "Example WebSocket API for real-time communication"

  tags = {
    Environment = "production"
  }
}
```

#### aws_apigatewayv2_route (WebSocket)
Defines message routing for WebSocket APIs.

```hcl
# Connect route - handles new connections
resource "aws_apigatewayv2_route" "connect" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$connect"

  authorization_type = "NONE"  # Can use AWS_IAM for authenticated connections
  target            = "integrations/${aws_apigatewayv2_integration.connect.id}"
}

# Disconnect route - handles connection termination
resource "aws_apigatewayv2_route" "disconnect" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$disconnect"
  target    = "integrations/${aws_apigatewayv2_integration.disconnect.id}"
}

# Default route - handles unmatched messages
resource "aws_apigatewayv2_route" "default" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.default.id}"
}

# Custom route - handles specific message types
resource "aws_apigatewayv2_route" "send_message" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "sendMessage"
  target    = "integrations/${aws_apigatewayv2_integration.send_message.id}"
}
```

#### aws_apigatewayv2_integration (WebSocket)
Connects WebSocket routes to backend handlers.

```hcl
resource "aws_apigatewayv2_integration" "connect" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  content_handling_strategy = "CONVERT_TO_TEXT"
  description       = "Connect integration"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_connect.invoke_arn
}

resource "aws_apigatewayv2_integration" "disconnect" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Disconnect integration"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_disconnect.invoke_arn
}

resource "aws_apigatewayv2_integration" "send_message" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Send message integration"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_send_message.invoke_arn
}
```

#### aws_apigatewayv2_stage (WebSocket)
Creates deployment stages for WebSocket APIs.

```hcl
resource "aws_apigatewayv2_stage" "websocket_prod" {
  api_id      = aws_apigatewayv2_api.websocket.id
  name        = "prod"
  auto_deploy = true

  # Throttling
  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  # Access logging
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.websocket_api_gw.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      connectionId   = "$context.connectionId"
      routeKey       = "$context.routeKey"
      status         = "$context.status"
      error          = "$context.error.message"
      integrationError = "$context.integrationErrorMessage"
      requestTime    = "$context.requestTime"
      responseLength = "$context.responseLength"
    })
  }

  tags = {
    Environment = "production"
  }
}
```

### WebSocket Connection Management
WebSocket APIs require special handling for connection management:

```hcl
# DynamoDB table to store connection IDs
resource "aws_dynamodb_table" "websocket_connections" {
  name           = "websocket-connections"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "connectionId"

  attribute {
    name = "connectionId"
    type = "S"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  tags = {
    Environment = "production"
  }
}

# IAM role for Lambda functions to manage connections
resource "aws_iam_role" "websocket_lambda_role" {
  name = "websocket-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "websocket_lambda_policy" {
  name = "websocket-lambda-policy"
  role = aws_iam_role.websocket_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "execute-api:ManageConnections"
        ]
        Resource = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:Scan"
        ]
        Resource = aws_dynamodb_table.websocket_connections.arn
      }
    ]
  })
}
```

### Advanced REST API Resources

#### aws_api_gateway_model
Defines data models for request/response validation.

```hcl
resource "aws_api_gateway_model" "user" {
  rest_api_id  = aws_api_gateway_rest_api.example.id
  name         = "User"
  content_type = "application/json"

  schema = jsonencode({
    "$schema": "http://json-schema.org/draft-04/schema#",
    "title": "User Schema",
    "type": "object",
    "properties": {
      "id": {
        "type": "string",
        "description": "User ID"
      },
      "name": {
        "type": "string",
        "description": "User name",
        "minLength": 1,
        "maxLength": 100
      },
      "email": {
        "type": "string",
        "format": "email",
        "description": "User email address"
      },
      "age": {
        "type": "integer",
        "minimum": 0,
        "maximum": 150
      }
    },
    "required": ["name", "email"],
    "additionalProperties": false
  })
}

resource "aws_api_gateway_model" "error" {
  rest_api_id  = aws_api_gateway_rest_api.example.id
  name         = "Error"
  content_type = "application/json"

  schema = jsonencode({
    "$schema": "http://json-schema.org/draft-04/schema#",
    "title": "Error Schema",
    "type": "object",
    "properties": {
      "message": {
        "type": "string"
      },
      "code": {
        "type": "string"
      }
    },
    "required": ["message"]
  })
}
```

#### aws_api_gateway_request_validator
Validates incoming requests against models and parameters.

```hcl
resource "aws_api_gateway_request_validator" "body_validator" {
  name                        = "body-validator"
  rest_api_id                = aws_api_gateway_rest_api.example.id
  validate_request_body      = true
  validate_request_parameters = false
}

resource "aws_api_gateway_request_validator" "params_validator" {
  name                        = "params-validator"
  rest_api_id                = aws_api_gateway_rest_api.example.id
  validate_request_body      = false
  validate_request_parameters = true
}

resource "aws_api_gateway_request_validator" "full_validator" {
  name                        = "full-validator"
  rest_api_id                = aws_api_gateway_rest_api.example.id
  validate_request_body      = true
  validate_request_parameters = true
}
```

#### aws_api_gateway_authorizer
Configures custom authorization for REST APIs.

```hcl
# Lambda Authorizer (Custom Authorizer)
resource "aws_api_gateway_authorizer" "lambda_auth" {
  name                   = "lambda-authorizer"
  rest_api_id           = aws_api_gateway_rest_api.example.id
  authorizer_uri        = aws_lambda_function.authorizer.invoke_arn
  authorizer_credentials = aws_iam_role.invocation_role.arn

  type                         = "TOKEN"  # TOKEN or REQUEST
  identity_source             = "method.request.header.Authorization"
  authorizer_result_ttl_in_seconds = 300

  # For REQUEST type authorizers
  # identity_source = "method.request.header.Authorization,method.request.querystring.user"
}

# Cognito User Pool Authorizer
resource "aws_api_gateway_authorizer" "cognito_auth" {
  name         = "cognito-authorizer"
  type         = "COGNITO_USER_POOLS"
  rest_api_id  = aws_api_gateway_rest_api.example.id
  provider_arns = [aws_cognito_user_pool.example.arn]
}
```

#### aws_api_gateway_gateway_response
Customizes error responses from API Gateway.

```hcl
resource "aws_api_gateway_gateway_response" "unauthorized" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  status_code   = "401"
  response_type = "UNAUTHORIZED"

  response_templates = {
    "application/json" = jsonencode({
      message = "Unauthorized access"
      error   = "$context.error.messageString"
    })
  }

  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
}

resource "aws_api_gateway_gateway_response" "bad_request" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  status_code   = "400"
  response_type = "BAD_REQUEST_BODY"

  response_templates = {
    "application/json" = jsonencode({
      message = "Invalid request body"
      error   = "$context.error.validationErrorString"
    })
  }
}
```

#### aws_api_gateway_usage_plan
Manages API usage plans for throttling and quotas.

```hcl
resource "aws_api_gateway_usage_plan" "premium" {
  name         = "premium-plan"
  description  = "Premium usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.example.id
    stage  = aws_api_gateway_stage.prod.stage_name

    # Per-method throttling
    throttle {
      path        = "/users/GET"
      rate_limit  = 100
      burst_limit = 200
    }
  }

  quota_settings {
    limit  = 10000
    period = "MONTH"
  }

  throttle_settings {
    rate_limit  = 500
    burst_limit = 1000
  }
}

resource "aws_api_gateway_api_key" "premium_key" {
  name        = "premium-api-key"
  description = "API key for premium users"
  enabled     = true

  tags = {
    Environment = "production"
  }
}

resource "aws_api_gateway_usage_plan_key" "premium_key_attachment" {
  key_id        = aws_api_gateway_api_key.premium_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.premium.id
}
```

#### CORS Configuration for REST APIs
CORS must be manually configured for REST APIs using OPTIONS methods.

```hcl
# OPTIONS method for CORS preflight
resource "aws_api_gateway_method" "cors_options" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_options" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.cors_options.http_method

  type = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "cors_options_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.cors_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_options_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.cors_options.http_method
  status_code = aws_api_gateway_method_response.cors_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods" = "'GET,POST,PUT,DELETE,OPTIONS'"
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}
```

#### aws_api_gateway_vpc_link
Enables private integrations with VPC resources.

```hcl
resource "aws_api_gateway_vpc_link" "example" {
  name        = "example-vpc-link"
  description = "VPC link for private integrations"
  target_arns = [aws_lb.internal.arn]

  tags = {
    Environment = "production"
  }
}

# Network Load Balancer for VPC Link
resource "aws_lb" "internal" {
  name               = "internal-nlb"
  internal           = true
  load_balancer_type = "network"
  subnets            = var.private_subnet_ids

  enable_deletion_protection = false

  tags = {
    Environment = "production"
  }
}
```

#### aws_api_gateway_domain_name
Configures custom domain names for APIs.

```hcl
resource "aws_api_gateway_domain_name" "example" {
  domain_name              = "api.example.com"
  regional_certificate_arn = aws_acm_certificate_validation.example.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = {
    Environment = "production"
  }
}

resource "aws_api_gateway_base_path_mapping" "example" {
  api_id      = aws_api_gateway_rest_api.example.id
  stage_name  = aws_api_gateway_stage.prod.stage_name
  domain_name = aws_api_gateway_domain_name.example.domain_name
  base_path   = "v1"  # Optional: maps to api.example.com/v1/
}

# Route53 record for custom domain
resource "aws_route53_record" "api" {
  name    = aws_api_gateway_domain_name.example.domain_name
  type    = "A"
  zone_id = var.hosted_zone_id

  alias {
    evaluate_target_health = true
    name                   = aws_api_gateway_domain_name.example.regional_domain_name
    zone_id                = aws_api_gateway_domain_name.example.regional_zone_id
  }
}
```

### REST API Best Practices

#### 1. Deployment Management
```hcl
# Use triggers to force redeployment on changes
resource "aws_api_gateway_deployment" "example" {
  depends_on = [
    aws_api_gateway_method.users_get,
    aws_api_gateway_integration.lambda,
    aws_api_gateway_method_response.response_200,
    aws_api_gateway_integration_response.response_200,
  ]

  rest_api_id = aws_api_gateway_rest_api.example.id

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.users.id,
      aws_api_gateway_method.users_get.id,
      aws_api_gateway_integration.lambda.id,
      aws_api_gateway_method_response.response_200.id,
      aws_api_gateway_integration_response.response_200.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}
```

#### 2. Error Handling
```hcl
# Multiple error responses
resource "aws_api_gateway_method_response" "error_400" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method
  status_code = "400"

  response_models = {
    "application/json" = aws_api_gateway_model.error.name
  }
}

resource "aws_api_gateway_method_response" "error_500" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method
  status_code = "500"

  response_models = {
    "application/json" = aws_api_gateway_model.error.name
  }
}
```

### Advanced HTTP API (v2) Features

#### aws_apigatewayv2_vpc_link
Enables private integrations for HTTP APIs with VPC resources.

```hcl
resource "aws_apigatewayv2_vpc_link" "example" {
  name               = "example-vpc-link"
  security_group_ids = [aws_security_group.vpc_link.id]
  subnet_ids         = var.private_subnet_ids

  tags = {
    Environment = "production"
  }
}

# Security group for VPC Link
resource "aws_security_group" "vpc_link" {
  name_prefix = "vpc-link-"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Environment = "production"
  }
}
```

#### aws_apigatewayv2_domain_name
Configures custom domain names for HTTP APIs.

```hcl
resource "aws_apigatewayv2_domain_name" "example" {
  domain_name = "api.example.com"

  domain_name_configuration {
    certificate_arn = aws_acm_certificate_validation.example.certificate_arn
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }

  # Mutual TLS authentication
  mutual_tls_authentication {
    truststore_uri     = "s3://example-bucket/truststore.pem"
    truststore_version = "1"
  }

  tags = {
    Environment = "production"
  }
}
```

#### aws_apigatewayv2_api_mapping
Maps APIs to custom domain names.

```hcl
resource "aws_apigatewayv2_api_mapping" "example" {
  api_id          = aws_apigatewayv2_api.example.id
  domain_name     = aws_apigatewayv2_domain_name.example.id
  stage           = aws_apigatewayv2_stage.prod.id
  api_mapping_key = "v1"  # Optional: maps to api.example.com/v1/
}

# Multiple API mappings on same domain
resource "aws_apigatewayv2_api_mapping" "users_api" {
  api_id          = aws_apigatewayv2_api.users.id
  domain_name     = aws_apigatewayv2_domain_name.example.id
  stage           = aws_apigatewayv2_stage.prod.id
  api_mapping_key = "users"
}

resource "aws_apigatewayv2_api_mapping" "orders_api" {
  api_id          = aws_apigatewayv2_api.orders.id
  domain_name     = aws_apigatewayv2_domain_name.example.id
  stage           = aws_apigatewayv2_stage.prod.id
  api_mapping_key = "orders"
}
```

#### Advanced Integration Patterns

##### Private ALB Integration
```hcl
resource "aws_apigatewayv2_integration" "alb_integration" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "HTTP_PROXY"

  connection_type = "VPC_LINK"
  connection_id   = aws_apigatewayv2_vpc_link.example.id

  integration_method = "ANY"
  integration_uri    = "http://${aws_lb.internal_alb.dns_name}/{proxy}"

  # TLS configuration for HTTPS backends
  tls_config {
    server_name_to_verify = "internal.example.com"
  }
}

# Application Load Balancer
resource "aws_lb" "internal_alb" {
  name               = "internal-alb"
  internal           = true
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = var.private_subnet_ids

  enable_deletion_protection = false

  tags = {
    Environment = "production"
  }
}
```

##### AWS Service Integration
```hcl
resource "aws_apigatewayv2_integration" "sqs_integration" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "AWS_PROXY"

  connection_type      = "INTERNET"
  credentials_arn      = aws_iam_role.api_gateway_sqs.arn
  integration_method   = "POST"
  integration_uri      = "arn:aws:apigateway:${var.region}:sqs:path/${aws_sqs_queue.example.name}"
  passthrough_behavior = "NEVER"

  request_parameters = {
    "overwrite:header.content-type" = "application/x-amz-json-1.0"
  }

  # Transform request to SQS format
  request_templates = {
    "application/json" = "Action=SendMessage&MessageBody=$input.body"
  }
}
```

##### Cloud Map Service Discovery Integration
```hcl
resource "aws_apigatewayv2_integration" "cloud_map_integration" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "HTTP_PROXY"

  connection_type = "VPC_LINK"
  connection_id   = aws_apigatewayv2_vpc_link.example.id

  integration_method = "ANY"
  integration_uri    = "http://${aws_service_discovery_service.backend.name}.${aws_service_discovery_private_dns_namespace.example.name}/{proxy}"
}

# Service Discovery
resource "aws_service_discovery_private_dns_namespace" "example" {
  name        = "example.local"
  description = "Private DNS namespace for service discovery"
  vpc         = var.vpc_id
}

resource "aws_service_discovery_service" "backend" {
  name = "backend-service"

  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.example.id

    dns_records {
      ttl  = 10
      type = "A"
    }

    routing_policy = "MULTIVALUE"
  }

  health_check_grace_period_seconds = 30
}
```

#### Parameter Mapping and Transformations
```hcl
resource "aws_apigatewayv2_integration" "parameter_mapping" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "HTTP_PROXY"

  connection_type    = "INTERNET"
  integration_method = "POST"
  integration_uri    = "https://backend.example.com/api"

  # Request parameter mapping
  request_parameters = {
    "overwrite:header.x-api-key"     = "$request.header.authorization"
    "overwrite:header.content-type"  = "application/json"
    "append:querystring.timestamp"   = "$context.requestTime"
    "remove:querystring.internal"    = ""
  }
}
```

### Advanced WebSocket API Patterns

#### Route Selection Expressions
WebSocket APIs use route selection expressions to determine how to route incoming messages.

```hcl
# Basic action-based routing
resource "aws_apigatewayv2_api" "chat_websocket" {
  name                       = "chat-websocket-api"
  protocol_type             = "WEBSOCKET"
  route_selection_expression = "$request.body.action"

  description = "Chat application WebSocket API"

  tags = {
    Environment = "production"
    Application = "chat"
  }
}

# Complex routing with nested properties
resource "aws_apigatewayv2_api" "gaming_websocket" {
  name                       = "gaming-websocket-api"
  protocol_type             = "WEBSOCKET"
  route_selection_expression = "$request.body.message.type"

  description = "Gaming WebSocket API with complex message routing"
}

# Header-based routing
resource "aws_apigatewayv2_api" "notification_websocket" {
  name                       = "notification-websocket-api"
  protocol_type             = "WEBSOCKET"
  route_selection_expression = "$request.header.message-type"

  description = "Notification WebSocket API with header-based routing"
}
```

#### Comprehensive Route Configuration

```hcl
# Chat application routes
resource "aws_apigatewayv2_route" "chat_connect" {
  api_id    = aws_apigatewayv2_api.chat_websocket.id
  route_key = "$connect"

  authorization_type = "AWS_IAM"  # Require authentication for connections
  target            = "integrations/${aws_apigatewayv2_integration.chat_connect.id}"
}

resource "aws_apigatewayv2_route" "chat_disconnect" {
  api_id    = aws_apigatewayv2_api.chat_websocket.id
  route_key = "$disconnect"
  target    = "integrations/${aws_apigatewayv2_integration.chat_disconnect.id}"
}

resource "aws_apigatewayv2_route" "chat_default" {
  api_id    = aws_apigatewayv2_api.chat_websocket.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.chat_default.id}"
}

# Custom message routes
resource "aws_apigatewayv2_route" "send_message" {
  api_id    = aws_apigatewayv2_api.chat_websocket.id
  route_key = "sendMessage"
  target    = "integrations/${aws_apigatewayv2_integration.send_message.id}"
}

resource "aws_apigatewayv2_route" "join_room" {
  api_id    = aws_apigatewayv2_api.chat_websocket.id
  route_key = "joinRoom"
  target    = "integrations/${aws_apigatewayv2_integration.join_room.id}"
}

resource "aws_apigatewayv2_route" "leave_room" {
  api_id    = aws_apigatewayv2_api.chat_websocket.id
  route_key = "leaveRoom"
  target    = "integrations/${aws_apigatewayv2_integration.leave_room.id}"
}

resource "aws_apigatewayv2_route" "get_users" {
  api_id    = aws_apigatewayv2_api.chat_websocket.id
  route_key = "getUsers"
  target    = "integrations/${aws_apigatewayv2_integration.get_users.id}"
}
```

#### WebSocket Integrations with Different Backends

```hcl
# Lambda integrations for different message types
resource "aws_apigatewayv2_integration" "chat_connect" {
  api_id           = aws_apigatewayv2_api.chat_websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Handle new WebSocket connections"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_connect.invoke_arn
}

resource "aws_apigatewayv2_integration" "send_message" {
  api_id           = aws_apigatewayv2_api.chat_websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Handle message sending"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_send_message.invoke_arn

  # Request transformation for message processing
  request_templates = {
    "application/json" = jsonencode({
      action      = "$input.json('$.action')"
      message     = "$input.json('$.message')"
      room        = "$input.json('$.room')"
      timestamp   = "$context.requestTime"
      connectionId = "$context.connectionId"
    })
  }
}

# HTTP integration for external services
resource "aws_apigatewayv2_integration" "external_notification" {
  api_id           = aws_apigatewayv2_api.chat_websocket.id
  integration_type = "HTTP"

  connection_type    = "INTERNET"
  integration_method = "POST"
  integration_uri    = "https://external-service.example.com/webhook"

  request_parameters = {
    "overwrite:header.authorization" = "Bearer ${var.external_service_token}"
    "overwrite:header.content-type"  = "application/json"
  }
}
```

#### Connection Management Infrastructure

```hcl
# Enhanced DynamoDB table for connection management
resource "aws_dynamodb_table" "websocket_connections" {
  name           = "websocket-connections"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "connectionId"

  attribute {
    name = "connectionId"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "roomId"
    type = "S"
  }

  # Global secondary index for user lookups
  global_secondary_index {
    name     = "UserIndex"
    hash_key = "userId"
  }

  # Global secondary index for room lookups
  global_secondary_index {
    name     = "RoomIndex"
    hash_key = "roomId"
  }

  # TTL for automatic cleanup
  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  tags = {
    Environment = "production"
    Application = "chat"
  }
}

# DynamoDB table for chat rooms
resource "aws_dynamodb_table" "chat_rooms" {
  name           = "chat-rooms"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "roomId"

  attribute {
    name = "roomId"
    type = "S"
  }

  tags = {
    Environment = "production"
    Application = "chat"
  }
}

# DynamoDB table for message history
resource "aws_dynamodb_table" "chat_messages" {
  name           = "chat-messages"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "roomId"
  range_key      = "timestamp"

  attribute {
    name = "roomId"
    type = "S"
  }

  attribute {
    name = "timestamp"
    type = "S"
  }

  # TTL for message retention
  ttl {
    attribute_name = "expiresAt"
    enabled        = true
  }

  tags = {
    Environment = "production"
    Application = "chat"
  }
}
```

#### Lambda Functions for WebSocket Management

```hcl
# Lambda function for connection handling
resource "aws_lambda_function" "websocket_connect" {
  filename         = "websocket_connect.zip"
  function_name    = "websocket-connect"
  role            = aws_iam_role.websocket_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      CONNECTIONS_TABLE = aws_dynamodb_table.websocket_connections.name
      ROOMS_TABLE      = aws_dynamodb_table.chat_rooms.name
    }
  }

  tags = {
    Environment = "production"
    Application = "chat"
  }
}

resource "aws_lambda_function" "websocket_disconnect" {
  filename         = "websocket_disconnect.zip"
  function_name    = "websocket-disconnect"
  role            = aws_iam_role.websocket_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      CONNECTIONS_TABLE = aws_dynamodb_table.websocket_connections.name
      ROOMS_TABLE      = aws_dynamodb_table.chat_rooms.name
    }
  }
}

resource "aws_lambda_function" "websocket_send_message" {
  filename         = "websocket_send_message.zip"
  function_name    = "websocket-send-message"
  role            = aws_iam_role.websocket_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      CONNECTIONS_TABLE = aws_dynamodb_table.websocket_connections.name
      MESSAGES_TABLE   = aws_dynamodb_table.chat_messages.name
      API_GATEWAY_ENDPOINT = aws_apigatewayv2_stage.websocket_prod.invoke_url
    }
  }
}

# Lambda permissions for API Gateway
resource "aws_lambda_permission" "websocket_connect" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.websocket_connect.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.chat_websocket.execution_arn}/*/*"
}

resource "aws_lambda_permission" "websocket_disconnect" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.websocket_disconnect.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.chat_websocket.execution_arn}/*/*"
}

resource "aws_lambda_permission" "websocket_send_message" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.websocket_send_message.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.chat_websocket.execution_arn}/*/*"
}
```

#### Enhanced IAM Roles and Policies

```hcl
resource "aws_iam_role" "websocket_lambda_role" {
  name = "websocket-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Environment = "production"
    Application = "chat"
  }
}

# Comprehensive policy for WebSocket Lambda functions
resource "aws_iam_role_policy" "websocket_lambda_policy" {
  name = "websocket-lambda-policy"
  role = aws_iam_role.websocket_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:ManageConnections"
        ]
        Resource = "${aws_apigatewayv2_api.chat_websocket.execution_arn}/*/*"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = [
          aws_dynamodb_table.websocket_connections.arn,
          "${aws_dynamodb_table.websocket_connections.arn}/index/*",
          aws_dynamodb_table.chat_rooms.arn,
          aws_dynamodb_table.chat_messages.arn
        ]
      }
    ]
  })
}
```

#### WebSocket API Monitoring and Logging

```hcl
# CloudWatch Log Group for WebSocket API
resource "aws_cloudwatch_log_group" "websocket_api_gw" {
  name              = "/aws/apigateway/websocket-api"
  retention_in_days = 14

  tags = {
    Environment = "production"
    Application = "chat"
  }
}

# Enhanced stage with comprehensive logging
resource "aws_apigatewayv2_stage" "websocket_prod" {
  api_id      = aws_apigatewayv2_api.chat_websocket.id
  name        = "prod"
  auto_deploy = true

  # Throttling settings
  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  # Comprehensive access logging
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.websocket_api_gw.arn
    format = jsonencode({
      requestId        = "$context.requestId"
      connectionId     = "$context.connectionId"
      routeKey         = "$context.routeKey"
      eventType        = "$context.eventType"
      status           = "$context.status"
      error            = "$context.error.message"
      integrationError = "$context.integrationErrorMessage"
      requestTime      = "$context.requestTime"
      responseLength   = "$context.responseLength"
      identity = {
        sourceIp    = "$context.identity.sourceIp"
        userAgent   = "$context.identity.userAgent"
        cognitoIdentityId = "$context.identity.cognitoIdentityId"
      }
    })
  }

  tags = {
    Environment = "production"
    Application = "chat"
  }
}

# CloudWatch alarms for WebSocket API
resource "aws_cloudwatch_metric_alarm" "websocket_errors" {
  alarm_name          = "websocket-api-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "IntegrationError"
  namespace           = "AWS/ApiGatewayV2"
  period              = "300"
  statistic           = "Sum"
  threshold           = "10"
  alarm_description   = "This metric monitors WebSocket API integration errors"

  dimensions = {
    ApiId = aws_apigatewayv2_api.chat_websocket.id
    Stage = aws_apigatewayv2_stage.websocket_prod.name
  }

  alarm_actions = [aws_sns_topic.alerts.arn]

  tags = {
    Environment = "production"
    Application = "chat"
  }
}
```

## Authentication & Authorization

### Overview of Authentication Methods

| Method | REST API | HTTP API | WebSocket API | Use Case |
|--------|----------|----------|---------------|----------|
| **None** | ✅ | ✅ | ✅ | Public APIs |
| **API Keys** | ✅ | ❌ | ❌ | Simple client identification |
| **IAM** | ✅ | ✅ | ✅ | AWS service-to-service |
| **Cognito User Pools** | ✅ | ✅ (via JWT) | ❌ | User authentication |
| **Lambda Authorizers** | ✅ | ✅ | ❌ | Custom authorization logic |
| **JWT Authorizers** | ❌ | ✅ | ❌ | Token-based auth (OIDC) |

### API Keys (REST API Only)

#### Basic API Key Configuration
```hcl
# Create API key
resource "aws_api_gateway_api_key" "client_key" {
  name        = "client-api-key"
  description = "API key for client application"
  enabled     = true

  tags = {
    Environment = "production"
    Client      = "mobile-app"
  }
}

# Usage plan with quotas and throttling
resource "aws_api_gateway_usage_plan" "basic_plan" {
  name         = "basic-usage-plan"
  description  = "Basic usage plan for API consumers"

  api_stages {
    api_id = aws_api_gateway_rest_api.example.id
    stage  = aws_api_gateway_stage.prod.stage_name
  }

  quota_settings {
    limit  = 1000
    offset = 0
    period = "DAY"
  }

  throttle_settings {
    rate_limit  = 100
    burst_limit = 200
  }
}

# Associate API key with usage plan
resource "aws_api_gateway_usage_plan_key" "basic_plan_key" {
  key_id        = aws_api_gateway_api_key.client_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.basic_plan.id
}

# Method requiring API key
resource "aws_api_gateway_method" "protected_method" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "GET"
  authorization = "NONE"

  api_key_required = true  # Require API key
}
```

### IAM Authentication

#### REST API with IAM
```hcl
resource "aws_api_gateway_method" "iam_protected" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "POST"
  authorization = "AWS_IAM"
}

# Resource policy for additional access control
resource "aws_api_gateway_rest_api_policy" "example" {
  rest_api_id = aws_api_gateway_rest_api.example.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = [
            "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/ApiGatewayRole",
            "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/ApiUser"
          ]
        }
        Action   = "execute-api:Invoke"
        Resource = "${aws_api_gateway_rest_api.example.execution_arn}/*"
      },
      {
        Effect = "Deny"
        Principal = "*"
        Action   = "execute-api:Invoke"
        Resource = "${aws_api_gateway_rest_api.example.execution_arn}/prod/DELETE/*"
        Condition = {
          StringNotEquals = {
            "aws:PrincipalTag/Department" = "Admin"
          }
        }
      }
    ]
  })
}
```

#### HTTP API with IAM
```hcl
resource "aws_apigatewayv2_route" "iam_protected" {
  api_id    = aws_apigatewayv2_api.example.id
  route_key = "POST /secure"

  authorization_type = "AWS_IAM"
  target            = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}

# IAM role for API access
resource "aws_iam_role" "api_client_role" {
  name = "api-client-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "api_client_policy" {
  name = "api-client-policy"
  role = aws_iam_role.api_client_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "execute-api:Invoke"
        Resource = "${aws_apigatewayv2_api.example.execution_arn}/*/*"
      }
    ]
  })
}
```

### Cognito User Pool Authentication

#### Cognito User Pool Setup
```hcl
resource "aws_cognito_user_pool" "example" {
  name = "example-user-pool"

  # Password policy
  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_symbols   = true
    require_uppercase = true
  }

  # User attributes
  username_attributes = ["email"]

  # Account recovery
  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }

  # Email configuration
  email_configuration {
    email_sending_account = "COGNITO_DEFAULT"
  }

  tags = {
    Environment = "production"
  }
}

resource "aws_cognito_user_pool_client" "example" {
  name         = "example-client"
  user_pool_id = aws_cognito_user_pool.example.id

  # OAuth settings
  allowed_oauth_flows                  = ["code", "implicit"]
  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_scopes                = ["email", "openid", "profile"]
  callback_urls                       = ["https://example.com/callback"]
  logout_urls                         = ["https://example.com/logout"]

  # Token validity
  access_token_validity  = 60    # minutes
  id_token_validity     = 60    # minutes
  refresh_token_validity = 30   # days

  # Prevent user existence errors
  prevent_user_existence_errors = "ENABLED"
}
```

#### REST API with Cognito User Pool Authorizer
```hcl
resource "aws_api_gateway_authorizer" "cognito" {
  name         = "cognito-authorizer"
  type         = "COGNITO_USER_POOLS"
  rest_api_id  = aws_api_gateway_rest_api.example.id
  provider_arns = [aws_cognito_user_pool.example.arn]

  # Optional: specify identity source
  identity_source = "method.request.header.Authorization"
}

resource "aws_api_gateway_method" "cognito_protected" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "GET"
  authorization = "COGNITO_USER_POOLS"
  authorizer_id = aws_api_gateway_authorizer.cognito.id
}
```

### Lambda Authorizers (Custom Authorizers)

#### TOKEN-based Lambda Authorizer (REST API)
```hcl
# Lambda function for authorization
resource "aws_lambda_function" "token_authorizer" {
  filename         = "token_authorizer.zip"
  function_name    = "token-authorizer"
  role            = aws_iam_role.authorizer_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      JWT_SECRET = var.jwt_secret
    }
  }

  tags = {
    Environment = "production"
  }
}

# TOKEN authorizer
resource "aws_api_gateway_authorizer" "token_auth" {
  name                   = "token-authorizer"
  rest_api_id           = aws_api_gateway_rest_api.example.id
  authorizer_uri        = aws_lambda_function.token_authorizer.invoke_arn
  authorizer_credentials = aws_iam_role.invocation_role.arn

  type                         = "TOKEN"
  identity_source             = "method.request.header.Authorization"
  authorizer_result_ttl_in_seconds = 300
}

# REQUEST-based Lambda Authorizer (REST API)
resource "aws_api_gateway_authorizer" "request_auth" {
  name                   = "request-authorizer"
  rest_api_id           = aws_api_gateway_rest_api.example.id
  authorizer_uri        = aws_lambda_function.request_authorizer.invoke_arn
  authorizer_credentials = aws_iam_role.invocation_role.arn

  type            = "REQUEST"
  identity_source = "method.request.header.Authorization,method.request.querystring.user"
  authorizer_result_ttl_in_seconds = 300
}

# IAM role for authorizer invocation
resource "aws_iam_role" "invocation_role" {
  name = "api-gateway-auth-invocation"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "invocation_policy" {
  name = "invocation-policy"
  role = aws_iam_role.invocation_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action   = "lambda:InvokeFunction"
        Effect   = "Allow"
        Resource = aws_lambda_function.token_authorizer.arn
      }
    ]
  })
}

# Lambda permission for API Gateway
resource "aws_lambda_permission" "authorizer_permission" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.token_authorizer.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.example.execution_arn}/authorizers/${aws_api_gateway_authorizer.token_auth.id}"
}
```

#### Lambda Authorizer for HTTP API
```hcl
resource "aws_apigatewayv2_authorizer" "lambda_auth" {
  api_id                            = aws_apigatewayv2_api.example.id
  authorizer_type                   = "REQUEST"
  authorizer_uri                    = aws_lambda_function.http_authorizer.invoke_arn
  identity_sources                  = ["$request.header.Authorization"]
  name                             = "lambda-authorizer"
  authorizer_payload_format_version = "2.0"
  authorizer_result_ttl_in_seconds  = 300
  enable_simple_responses           = true
}

resource "aws_apigatewayv2_route" "lambda_protected" {
  api_id    = aws_apigatewayv2_api.example.id
  route_key = "GET /protected"

  authorization_type = "CUSTOM"
  authorizer_id      = aws_apigatewayv2_authorizer.lambda_auth.id
  target            = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}

# Lambda function for HTTP API authorization
resource "aws_lambda_function" "http_authorizer" {
  filename         = "http_authorizer.zip"
  function_name    = "http-api-authorizer"
  role            = aws_iam_role.authorizer_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      JWT_SECRET = var.jwt_secret
    }
  }
}

resource "aws_lambda_permission" "http_authorizer_permission" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.http_authorizer.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.example.execution_arn}/authorizers/${aws_apigatewayv2_authorizer.lambda_auth.id}"
}
```

### JWT Authorizers (HTTP API Only)

#### JWT Authorizer with Cognito
```hcl
resource "aws_apigatewayv2_authorizer" "jwt_cognito" {
  api_id           = aws_apigatewayv2_api.example.id
  authorizer_type  = "JWT"
  identity_sources = ["$request.header.Authorization"]
  name             = "jwt-cognito-authorizer"

  jwt_configuration {
    audience = [aws_cognito_user_pool_client.example.id]
    issuer   = "https://cognito-idp.${var.region}.amazonaws.com/${aws_cognito_user_pool.example.id}"
  }
}

resource "aws_apigatewayv2_route" "jwt_protected" {
  api_id    = aws_apigatewayv2_api.example.id
  route_key = "GET /jwt-protected"

  authorization_type = "JWT"
  authorizer_id      = aws_apigatewayv2_authorizer.jwt_cognito.id
  target            = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}
```

#### JWT Authorizer with External OIDC Provider
```hcl
resource "aws_apigatewayv2_authorizer" "jwt_external" {
  api_id           = aws_apigatewayv2_api.example.id
  authorizer_type  = "JWT"
  identity_sources = ["$request.header.Authorization"]
  name             = "jwt-external-authorizer"

  jwt_configuration {
    audience = ["api.example.com"]
    issuer   = "https://auth.example.com"
  }
}

# Route with scopes
resource "aws_apigatewayv2_route" "scoped_route" {
  api_id    = aws_apigatewayv2_api.example.id
  route_key = "POST /admin"

  authorization_type   = "JWT"
  authorizer_id        = aws_apigatewayv2_authorizer.jwt_external.id
  authorization_scopes = ["admin:write", "users:manage"]
  target              = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}
```

### Authorization Best Practices

#### 1. Multi-layer Security
```hcl
# Combine multiple authorization methods
resource "aws_api_gateway_method" "multi_auth" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.admin.id
  http_method   = "DELETE"
  authorization = "AWS_IAM"

  # Also require API key
  api_key_required = true
}

# Resource policy for additional restrictions
resource "aws_api_gateway_rest_api_policy" "restrictive" {
  rest_api_id = aws_api_gateway_rest_api.example.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = "*"
        Action   = "execute-api:Invoke"
        Resource = "${aws_api_gateway_rest_api.example.execution_arn}/*"
        Condition = {
          IpAddress = {
            "aws:SourceIp" = var.allowed_ip_ranges
          }
        }
      }
    ]
  })
}
```

#### 2. Fine-grained Permissions
```hcl
# Method-specific authorization
resource "aws_api_gateway_method" "read_only" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "GET"
  authorization = "AWS_IAM"
}

resource "aws_api_gateway_method" "admin_only" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "DELETE"
  authorization = "COGNITO_USER_POOLS"
  authorizer_id = aws_api_gateway_authorizer.admin_cognito.id
}

# Separate authorizer for admin functions
resource "aws_api_gateway_authorizer" "admin_cognito" {
  name         = "admin-cognito-authorizer"
  type         = "COGNITO_USER_POOLS"
  rest_api_id  = aws_api_gateway_rest_api.example.id
  provider_arns = [aws_cognito_user_pool.admin.arn]
}
```

#### 3. Error Handling and Security
```hcl
# Custom gateway responses for security
resource "aws_api_gateway_gateway_response" "unauthorized" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  status_code   = "401"
  response_type = "UNAUTHORIZED"

  response_templates = {
    "application/json" = jsonencode({
      message = "Unauthorized"
      # Don't expose internal error details
    })
  }

  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
    "gatewayresponse.header.WWW-Authenticate" = "'Bearer'"
  }
}

resource "aws_api_gateway_gateway_response" "access_denied" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  status_code   = "403"
  response_type = "ACCESS_DENIED"

  response_templates = {
    "application/json" = jsonencode({
      message = "Access denied"
    })
  }
}
```

#### 4. Monitoring and Auditing
```hcl
# CloudWatch Log Group for authorizer logs
resource "aws_cloudwatch_log_group" "authorizer_logs" {
  name              = "/aws/lambda/authorizer"
  retention_in_days = 30

  tags = {
    Environment = "production"
    Purpose     = "security-audit"
  }
}

# CloudWatch alarms for authorization failures
resource "aws_cloudwatch_metric_alarm" "auth_failures" {
  alarm_name          = "api-gateway-auth-failures"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "4XXError"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Sum"
  threshold           = "50"
  alarm_description   = "High number of authentication failures"

  dimensions = {
    ApiName = aws_api_gateway_rest_api.example.name
    Stage   = aws_api_gateway_stage.prod.stage_name
  }

  alarm_actions = [aws_sns_topic.security_alerts.arn]
}

# WAF for additional protection (REST API only)
resource "aws_wafv2_web_acl" "api_protection" {
  name  = "api-gateway-protection"
  scope = "REGIONAL"

  default_action {
    allow {}
  }

  # Rate limiting rule
  rule {
    name     = "RateLimitRule"
    priority = 1

    action {
      block {}
    }

    statement {
      rate_based_statement {
        limit              = 2000
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitRule"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "APIGatewayWebACL"
    sampled_requests_enabled   = true
  }

  tags = {
    Environment = "production"
  }
}

# Associate WAF with API Gateway stage
resource "aws_wafv2_web_acl_association" "api_gateway" {
  resource_arn = aws_api_gateway_stage.prod.arn
  web_acl_arn  = aws_wafv2_web_acl.api_protection.arn
}
```

## Integration Patterns

### Overview of Integration Types

| Integration Type | REST API | HTTP API | Description | Use Case |
|------------------|----------|----------|-------------|----------|
| **AWS_PROXY** | ✅ | ✅ | Lambda proxy integration | Simple Lambda functions |
| **AWS** | ✅ | ✅ | AWS service integration | Custom request/response mapping |
| **HTTP_PROXY** | ✅ | ✅ | HTTP proxy integration | Pass-through to HTTP backends |
| **HTTP** | ✅ | ✅ | HTTP integration | Custom HTTP transformations |
| **MOCK** | ✅ | ❌ | Mock integration | Testing and prototyping |

### Lambda Integrations

#### AWS_PROXY Integration (Lambda Proxy)
The simplest integration type that passes the entire request to Lambda and expects a specific response format.

```hcl
# REST API Lambda Proxy Integration
resource "aws_api_gateway_integration" "lambda_proxy" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_get.http_method

  integration_http_method = "POST"  # Always POST for Lambda
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.users_handler.invoke_arn

  # No request/response templates needed - handled by Lambda
}

# HTTP API Lambda Proxy Integration
resource "aws_apigatewayv2_integration" "lambda_proxy_v2" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Lambda proxy integration"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.users_handler.invoke_arn

  # Payload format version for HTTP API
  payload_format_version = "2.0"  # or "1.0" for compatibility
}

# Lambda function for proxy integration
resource "aws_lambda_function" "users_handler" {
  filename         = "users_handler.zip"
  function_name    = "users-handler"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      TABLE_NAME = aws_dynamodb_table.users.name
    }
  }
}

# Lambda permission for API Gateway
resource "aws_lambda_permission" "api_gateway" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.users_handler.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.example.execution_arn}/*/*"
}
```

#### AWS Integration (Lambda Custom)
Provides full control over request/response transformation.

```hcl
resource "aws_api_gateway_integration" "lambda_custom" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method

  integration_http_method = "POST"
  type                   = "AWS"
  uri                    = aws_lambda_function.users_processor.invoke_arn

  # Request transformation
  request_templates = {
    "application/json" = jsonencode({
      userId    = "$input.params('id')"
      userData  = "$input.json('$')"
      timestamp = "$context.requestTime"
      sourceIp  = "$context.identity.sourceIp"
    })
  }

  # Request parameter mapping
  request_parameters = {
    "integration.request.header.X-User-ID" = "method.request.header.user-id"
  }
}

# Method response for custom integration
resource "aws_api_gateway_method_response" "lambda_custom_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }

  response_models = {
    "application/json" = aws_api_gateway_model.user_response.name
  }
}

# Integration response for custom integration
resource "aws_api_gateway_integration_response" "lambda_custom_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method
  status_code = aws_api_gateway_method_response.lambda_custom_200.status_code

  # Response transformation
  response_templates = {
    "application/json" = jsonencode({
      success = true
      data    = "$input.json('$')"
      message = "User processed successfully"
    })
  }

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }
}
```

### HTTP Integrations

#### HTTP_PROXY Integration
Passes requests directly to HTTP backends with minimal transformation.

```hcl
# REST API HTTP Proxy
resource "aws_api_gateway_integration" "http_proxy" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.proxy.id
  http_method = aws_api_gateway_method.proxy_any.http_method

  type                    = "HTTP_PROXY"
  integration_http_method = "ANY"
  uri                     = "https://backend.example.com/{proxy}"

  # Connection settings
  connection_type = "INTERNET"  # or "VPC_LINK"
  timeout_milliseconds = 29000

  # Request parameter mapping
  request_parameters = {
    "integration.request.path.proxy"           = "method.request.path.proxy"
    "integration.request.header.Authorization" = "method.request.header.Authorization"
  }
}

# HTTP API HTTP Proxy
resource "aws_apigatewayv2_integration" "http_proxy_v2" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "HTTP_PROXY"

  connection_type    = "INTERNET"
  integration_method = "ANY"
  integration_uri    = "https://backend.example.com/{proxy}"

  # Request parameter mapping
  request_parameters = {
    "overwrite:path" = "$request.path.proxy"
  }
}
```

#### HTTP Integration (Custom)
Provides full control over HTTP request/response transformation.

```hcl
resource "aws_api_gateway_integration" "http_custom" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.webhook.id
  http_method = aws_api_gateway_method.webhook_post.http_method

  type                    = "HTTP"
  integration_http_method = "POST"
  uri                     = "https://external-api.example.com/webhook"

  # Request transformation
  request_templates = {
    "application/json" = jsonencode({
      event_type = "api_gateway"
      payload    = "$input.json('$')"
      metadata = {
        timestamp = "$context.requestTime"
        requestId = "$context.requestId"
        sourceIp  = "$context.identity.sourceIp"
      }
    })
  }

  # Custom headers
  request_parameters = {
    "integration.request.header.X-API-Key"    = "'${var.external_api_key}'"
    "integration.request.header.Content-Type" = "'application/json'"
  }
}
```

### AWS Service Integrations

#### DynamoDB Integration
Direct integration with DynamoDB without Lambda.

```hcl
# DynamoDB PutItem integration
resource "aws_api_gateway_integration" "dynamodb_put" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method

  type                    = "AWS"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:dynamodb:action/PutItem"
  credentials             = aws_iam_role.api_gateway_dynamodb.arn

  request_templates = {
    "application/json" = jsonencode({
      TableName = aws_dynamodb_table.users.name
      Item = {
        id = {
          S = "$context.requestId"
        }
        name = {
          S = "$input.path('$.name')"
        }
        email = {
          S = "$input.path('$.email')"
        }
        created_at = {
          S = "$context.requestTime"
        }
      }
    })
  }
}

# DynamoDB Query integration
resource "aws_api_gateway_integration" "dynamodb_query" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.user_id.id
  http_method = aws_api_gateway_method.user_get.http_method

  type                    = "AWS"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:dynamodb:action/GetItem"
  credentials             = aws_iam_role.api_gateway_dynamodb.arn

  request_templates = {
    "application/json" = jsonencode({
      TableName = aws_dynamodb_table.users.name
      Key = {
        id = {
          S = "$input.params('id')"
        }
      }
    })
  }
}

# IAM role for DynamoDB access
resource "aws_iam_role" "api_gateway_dynamodb" {
  name = "api-gateway-dynamodb-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "api_gateway_dynamodb" {
  name = "api-gateway-dynamodb-policy"
  role = aws_iam_role.api_gateway_dynamodb.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = aws_dynamodb_table.users.arn
      }
    ]
  })
}
```

#### SQS Integration
Direct integration with Amazon SQS.

```hcl
resource "aws_api_gateway_integration" "sqs_send" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.messages.id
  http_method = aws_api_gateway_method.messages_post.http_method

  type                    = "AWS"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:sqs:path/${aws_sqs_queue.messages.name}"
  credentials             = aws_iam_role.api_gateway_sqs.arn

  request_parameters = {
    "integration.request.header.Content-Type" = "'application/x-amz-json-1.0'"
  }

  request_templates = {
    "application/json" = "Action=SendMessage&MessageBody=$input.body"
  }
}

# HTTP API SQS Integration
resource "aws_apigatewayv2_integration" "sqs_send_v2" {
  api_id           = aws_apigatewayv2_api.example.id
  integration_type = "AWS_PROXY"

  connection_type      = "INTERNET"
  credentials_arn      = aws_iam_role.api_gateway_sqs.arn
  integration_method   = "POST"
  integration_uri      = "arn:aws:apigateway:${var.region}:sqs:path/${aws_sqs_queue.messages.name}"
  passthrough_behavior = "NEVER"

  request_parameters = {
    "overwrite:header.content-type" = "application/x-amz-json-1.0"
  }
}

# SQS Queue
resource "aws_sqs_queue" "messages" {
  name                      = "api-messages"
  delay_seconds             = 0
  max_message_size          = 262144
  message_retention_seconds = 1209600
  receive_wait_time_seconds = 10

  tags = {
    Environment = "production"
  }
}

# IAM role for SQS access
resource "aws_iam_role" "api_gateway_sqs" {
  name = "api-gateway-sqs-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "api_gateway_sqs" {
  name = "api-gateway-sqs-policy"
  role = aws_iam_role.api_gateway_sqs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sqs:SendMessage",
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage"
        ]
        Resource = aws_sqs_queue.messages.arn
      }
    ]
  })
}
```

#### S3 Integration
Direct integration with Amazon S3.

```hcl
resource "aws_api_gateway_integration" "s3_get" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.files.id
  http_method = aws_api_gateway_method.files_get.http_method

  type                    = "AWS"
  integration_http_method = "GET"
  uri                     = "arn:aws:apigateway:${var.region}:s3:path/${aws_s3_bucket.files.bucket}/{key}"
  credentials             = aws_iam_role.api_gateway_s3.arn

  request_parameters = {
    "integration.request.path.key" = "method.request.path.filename"
  }
}

resource "aws_api_gateway_integration" "s3_put" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.files.id
  http_method = aws_api_gateway_method.files_put.http_method

  type                    = "AWS"
  integration_http_method = "PUT"
  uri                     = "arn:aws:apigateway:${var.region}:s3:path/${aws_s3_bucket.files.bucket}/{key}"
  credentials             = aws_iam_role.api_gateway_s3.arn

  request_parameters = {
    "integration.request.path.key"                = "method.request.path.filename"
    "integration.request.header.Content-Type"     = "method.request.header.Content-Type"
    "integration.request.header.x-amz-acl"        = "'bucket-owner-full-control'"
  }
}
```

### Mock Integrations (REST API Only)

#### Basic Mock Integration
```hcl
resource "aws_api_gateway_integration" "mock" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.health.id
  http_method = aws_api_gateway_method.health_get.http_method

  type = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "mock_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.health.id
  http_method = aws_api_gateway_method.health_get.http_method
  status_code = "200"

  response_models = {
    "application/json" = "Empty"
  }
}

resource "aws_api_gateway_integration_response" "mock_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.health.id
  http_method = aws_api_gateway_method.health_get.http_method
  status_code = aws_api_gateway_method_response.mock_200.status_code

  response_templates = {
    "application/json" = jsonencode({
      status  = "healthy"
      version = "1.0.0"
      timestamp = "$context.requestTime"
    })
  }
}
```

#### Conditional Mock Responses
```hcl
resource "aws_api_gateway_integration" "conditional_mock" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.test.id
  http_method = aws_api_gateway_method.test_get.http_method

  type = "MOCK"

  request_templates = {
    "application/json" = <<EOF
#if($input.params('error') == 'true')
{
  "statusCode": 500
}
#else
{
  "statusCode": 200
}
#end
EOF
  }
}

# Multiple response codes for conditional mock
resource "aws_api_gateway_method_response" "mock_500" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.test.id
  http_method = aws_api_gateway_method.test_get.http_method
  status_code = "500"
}

resource "aws_api_gateway_integration_response" "mock_500" {
  rest_api_id       = aws_api_gateway_rest_api.example.id
  resource_id       = aws_api_gateway_resource.test.id
  http_method       = aws_api_gateway_method.test_get.http_method
  status_code       = aws_api_gateway_method_response.mock_500.status_code
  selection_pattern = "5\\d{2}"  # Match 5xx status codes

  response_templates = {
    "application/json" = jsonencode({
      error   = "Internal Server Error"
      message = "This is a mock error response"
    })
  }
}
```

## Advanced Features

### CORS Configuration

#### REST API CORS (Manual Configuration)
REST APIs require manual CORS configuration using OPTIONS methods.

```hcl
# CORS-enabled resource
resource "aws_api_gateway_resource" "cors_enabled" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  parent_id   = aws_api_gateway_rest_api.example.root_resource_id
  path_part   = "cors-enabled"
}

# Main method (e.g., GET)
resource "aws_api_gateway_method" "cors_get" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.cors_enabled.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_get" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.cors_enabled.id
  http_method = aws_api_gateway_method.cors_get.http_method

  type                   = "AWS_PROXY"
  integration_http_method = "POST"
  uri                    = aws_lambda_function.example.invoke_arn
}

resource "aws_api_gateway_method_response" "cors_get_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.cors_enabled.id
  http_method = aws_api_gateway_method.cors_get.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = true
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
  }
}

resource "aws_api_gateway_integration_response" "cors_get_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.cors_enabled.id
  http_method = aws_api_gateway_method.cors_get.http_method
  status_code = aws_api_gateway_method_response.cors_get_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods" = "'GET,POST,PUT,DELETE,OPTIONS'"
  }
}

# OPTIONS method for CORS preflight
resource "aws_api_gateway_method" "cors_options" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.cors_enabled.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_options" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.cors_enabled.id
  http_method = aws_api_gateway_method.cors_options.http_method

  type = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "cors_options_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.cors_enabled.id
  http_method = aws_api_gateway_method.cors_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = true
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Max-Age"       = true
  }
}

resource "aws_api_gateway_integration_response" "cors_options_200" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.cors_enabled.id
  http_method = aws_api_gateway_method.cors_options.http_method
  status_code = aws_api_gateway_method_response.cors_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods" = "'GET,POST,PUT,DELETE,OPTIONS'"
    "method.response.header.Access-Control-Max-Age"       = "'86400'"
  }
}
```

#### HTTP API CORS (Automatic Configuration)
HTTP APIs provide built-in CORS support.

```hcl
resource "aws_apigatewayv2_api" "cors_enabled" {
  name          = "cors-enabled-api"
  protocol_type = "HTTP"

  cors_configuration {
    allow_credentials = false
    allow_headers = [
      "content-type",
      "x-amz-date",
      "authorization",
      "x-api-key",
      "x-amz-security-token",
      "x-amz-user-agent"
    ]
    allow_methods = ["*"]
    allow_origins = ["https://example.com", "https://app.example.com"]
    expose_headers = ["date", "keep-alive"]
    max_age       = 86400
  }
}

# Advanced CORS with specific origins and credentials
resource "aws_apigatewayv2_api" "advanced_cors" {
  name          = "advanced-cors-api"
  protocol_type = "HTTP"

  cors_configuration {
    allow_credentials = true
    allow_headers = [
      "authorization",
      "content-type",
      "x-requested-with"
    ]
    allow_methods = ["GET", "POST", "PUT", "DELETE"]
    allow_origins = [
      "https://app.example.com",
      "https://admin.example.com"
    ]
    expose_headers = [
      "x-custom-header",
      "x-request-id"
    ]
    max_age = 3600
  }
}
```

### Caching (REST API Only)

#### Method-level Caching
```hcl
resource "aws_api_gateway_method" "cached_method" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "GET"
  authorization = "NONE"

  # Enable caching for this method
  request_parameters = {
    "method.request.querystring.limit"  = false
    "method.request.querystring.offset" = false
  }
}

resource "aws_api_gateway_integration" "cached_integration" {
  rest_api_id = aws_api_gateway_rest_api.example.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.cached_method.http_method

  type                   = "AWS_PROXY"
  integration_http_method = "POST"
  uri                    = aws_lambda_function.users_handler.invoke_arn

  # Cache key parameters
  cache_key_parameters = [
    "method.request.querystring.limit",
    "method.request.querystring.offset"
  ]

  cache_namespace = "users-cache"
}

# Stage with caching enabled
resource "aws_api_gateway_stage" "cached_stage" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "prod"

  # Enable caching
  cache_cluster_enabled = true
  cache_cluster_size    = "0.5"  # 0.5, 1.6, 6.1, 13.5, 28.4, 58.2, 118, 237 GB

  # Method-specific cache settings
  cache_key_parameters = [
    "method.request.querystring.limit",
    "method.request.querystring.offset"
  ]

  # Cache settings
  settings {
    caching_enabled      = true
    cache_ttl_in_seconds = 300
    cache_data_encrypted = true

    # Per-method cache settings
    "users/GET/caching/enabled"           = true
    "users/GET/caching/ttl_in_seconds"    = 600
    "users/GET/caching/data_encrypted"    = true
    "users/GET/caching/unauthorized_cache_control_header_strategy" = "SUCCEED_WITH_RESPONSE_HEADER"
  }
}
```

### Throttling and Rate Limiting

#### Stage-level Throttling
```hcl
# REST API Stage Throttling
resource "aws_api_gateway_stage" "throttled_stage" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "prod"

  # Global throttling settings
  throttle_settings {
    rate_limit  = 1000  # requests per second
    burst_limit = 2000  # burst capacity
  }

  # Method-specific throttling
  settings {
    # Throttle specific methods
    "users/GET/throttling/rate_limit"  = 500
    "users/GET/throttling/burst_limit" = 1000
    "users/POST/throttling/rate_limit" = 100
    "users/POST/throttling/burst_limit" = 200
  }
}

# HTTP API Stage Throttling
resource "aws_apigatewayv2_stage" "throttled_stage_v2" {
  api_id      = aws_apigatewayv2_api.example.id
  name        = "prod"
  auto_deploy = true

  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  # Route-specific throttling
  route_settings {
    route_key                = "GET /users"
    throttling_rate_limit    = 500
    throttling_burst_limit   = 1000
    detailed_metrics_enabled = true
  }

  route_settings {
    route_key                = "POST /users"
    throttling_rate_limit    = 100
    throttling_burst_limit   = 200
    detailed_metrics_enabled = true
  }
}
```

#### Usage Plans with Advanced Throttling
```hcl
resource "aws_api_gateway_usage_plan" "tiered_plan" {
  name         = "tiered-usage-plan"
  description  = "Multi-tier usage plan with different limits"

  api_stages {
    api_id = aws_api_gateway_rest_api.example.id
    stage  = aws_api_gateway_stage.prod.stage_name

    # Per-method throttling within usage plan
    throttle {
      path        = "/users/GET"
      rate_limit  = 100
      burst_limit = 200
    }

    throttle {
      path        = "/admin/*/DELETE"
      rate_limit  = 10
      burst_limit = 20
    }
  }

  # Overall quota
  quota_settings {
    limit  = 10000
    offset = 0
    period = "MONTH"
  }

  # Overall throttling
  throttle_settings {
    rate_limit  = 500
    burst_limit = 1000
  }
}
```

### Request Validation

#### Comprehensive Request Validation
```hcl
# Request validator
resource "aws_api_gateway_request_validator" "comprehensive" {
  name                        = "comprehensive-validator"
  rest_api_id                = aws_api_gateway_rest_api.example.id
  validate_request_body      = true
  validate_request_parameters = true
}

# Model for request validation
resource "aws_api_gateway_model" "user_create" {
  rest_api_id  = aws_api_gateway_rest_api.example.id
  name         = "UserCreate"
  content_type = "application/json"

  schema = jsonencode({
    "$schema": "http://json-schema.org/draft-04/schema#",
    "title": "User Create Schema",
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "minLength": 1,
        "maxLength": 100,
        "pattern": "^[a-zA-Z\\s]+$"
      },
      "email": {
        "type": "string",
        "format": "email"
      },
      "age": {
        "type": "integer",
        "minimum": 18,
        "maximum": 120
      },
      "preferences": {
        "type": "object",
        "properties": {
          "newsletter": {
            "type": "boolean"
          },
          "theme": {
            "type": "string",
            "enum": ["light", "dark"]
          }
        },
        "additionalProperties": false
      }
    },
    "required": ["name", "email"],
    "additionalProperties": false
  })
}

# Method with comprehensive validation
resource "aws_api_gateway_method" "validated_method" {
  rest_api_id   = aws_api_gateway_rest_api.example.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "POST"
  authorization = "NONE"

  # Request validation
  request_validator_id = aws_api_gateway_request_validator.comprehensive.id

  # Required parameters
  request_parameters = {
    "method.request.header.Content-Type"    = true
    "method.request.querystring.version"    = false
    "method.request.header.X-API-Version"   = false
  }

  # Request model
  request_models = {
    "application/json" = aws_api_gateway_model.user_create.name
  }
}
```

### Custom Domain Names

#### REST API Custom Domain
```hcl
# ACM Certificate
resource "aws_acm_certificate" "api_cert" {
  domain_name       = "api.example.com"
  validation_method = "DNS"

  subject_alternative_names = [
    "*.api.example.com"
  ]

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Environment = "production"
  }
}

# Certificate validation
resource "aws_acm_certificate_validation" "api_cert" {
  certificate_arn         = aws_acm_certificate.api_cert.arn
  validation_record_fqdns = [for record in aws_route53_record.cert_validation : record.fqdn]
}

# Route53 records for certificate validation
resource "aws_route53_record" "cert_validation" {
  for_each = {
    for dvo in aws_acm_certificate.api_cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = var.hosted_zone_id
}

# Custom domain name
resource "aws_api_gateway_domain_name" "api_domain" {
  domain_name              = "api.example.com"
  regional_certificate_arn = aws_acm_certificate_validation.api_cert.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  security_policy = "TLS_1_2"

  tags = {
    Environment = "production"
  }
}

# Base path mapping
resource "aws_api_gateway_base_path_mapping" "api_mapping" {
  api_id      = aws_api_gateway_rest_api.example.id
  stage_name  = aws_api_gateway_stage.prod.stage_name
  domain_name = aws_api_gateway_domain_name.api_domain.domain_name
  base_path   = "v1"
}

# Route53 alias record
resource "aws_route53_record" "api_alias" {
  name    = aws_api_gateway_domain_name.api_domain.domain_name
  type    = "A"
  zone_id = var.hosted_zone_id

  alias {
    evaluate_target_health = true
    name                   = aws_api_gateway_domain_name.api_domain.regional_domain_name
    zone_id                = aws_api_gateway_domain_name.api_domain.regional_zone_id
  }
}
```

#### HTTP API Custom Domain
```hcl
resource "aws_apigatewayv2_domain_name" "api_domain_v2" {
  domain_name = "api-v2.example.com"

  domain_name_configuration {
    certificate_arn = aws_acm_certificate_validation.api_cert.certificate_arn
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }

  # Mutual TLS configuration
  mutual_tls_authentication {
    truststore_uri     = "s3://${aws_s3_bucket.truststore.bucket}/truststore.pem"
    truststore_version = "1"
  }

  tags = {
    Environment = "production"
  }
}

resource "aws_apigatewayv2_api_mapping" "api_mapping_v2" {
  api_id          = aws_apigatewayv2_api.example.id
  domain_name     = aws_apigatewayv2_domain_name.api_domain_v2.id
  stage           = aws_apigatewayv2_stage.prod.id
  api_mapping_key = "v2"
}

# Route53 record for HTTP API
resource "aws_route53_record" "api_v2_alias" {
  name    = aws_apigatewayv2_domain_name.api_domain_v2.domain_name
  type    = "A"
  zone_id = var.hosted_zone_id

  alias {
    evaluate_target_health = true
    name                   = aws_apigatewayv2_domain_name.api_domain_v2.domain_name_configuration[0].target_domain_name
    zone_id                = aws_apigatewayv2_domain_name.api_domain_v2.domain_name_configuration[0].hosted_zone_id
  }
}
```

## Deployment Strategies

### Deployment Best Practices

#### 1. Dependency Management
```hcl
# Proper dependency ordering for REST API
resource "aws_api_gateway_deployment" "example" {
  depends_on = [
    # All methods must be created first
    aws_api_gateway_method.users_get,
    aws_api_gateway_method.users_post,
    aws_api_gateway_method.users_put,
    aws_api_gateway_method.users_delete,

    # All integrations must be created
    aws_api_gateway_integration.users_get,
    aws_api_gateway_integration.users_post,
    aws_api_gateway_integration.users_put,
    aws_api_gateway_integration.users_delete,

    # All method responses
    aws_api_gateway_method_response.users_get_200,
    aws_api_gateway_method_response.users_post_201,

    # All integration responses
    aws_api_gateway_integration_response.users_get_200,
    aws_api_gateway_integration_response.users_post_201,
  ]

  rest_api_id = aws_api_gateway_rest_api.example.id

  # Force redeployment when API changes
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.users.id,
      aws_api_gateway_method.users_get.id,
      aws_api_gateway_method.users_post.id,
      aws_api_gateway_integration.users_get.id,
      aws_api_gateway_integration.users_post.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}
```

#### 2. Stage Management Strategy
```hcl
# Development stage
resource "aws_api_gateway_stage" "dev" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "dev"

  # Development-specific settings
  throttle_settings {
    rate_limit  = 100
    burst_limit = 200
  }

  # Enable detailed logging for development
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gw_dev.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      requestTime    = "$context.requestTime"
      httpMethod     = "$context.httpMethod"
      resourcePath   = "$context.resourcePath"
      status         = "$context.status"
      responseLength = "$context.responseLength"
      error          = "$context.error.message"
    })
  }

  xray_tracing_enabled = true

  tags = {
    Environment = "development"
  }
}

# Staging stage
resource "aws_api_gateway_stage" "staging" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "staging"

  # Production-like settings
  throttle_settings {
    rate_limit  = 500
    burst_limit = 1000
  }

  # Caching for performance testing
  cache_cluster_enabled = true
  cache_cluster_size    = "0.5"

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gw_staging.arn
    format = jsonencode({
      requestId    = "$context.requestId"
      status       = "$context.status"
      responseTime = "$context.responseTime"
    })
  }

  tags = {
    Environment = "staging"
  }
}

# Production stage
resource "aws_api_gateway_stage" "prod" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "prod"

  # Production settings
  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  # Production caching
  cache_cluster_enabled = true
  cache_cluster_size    = "1.6"

  # Minimal logging for production
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gw_prod.arn
    format = jsonencode({
      requestId = "$context.requestId"
      status    = "$context.status"
      error     = "$context.error.message"
    })
  }

  tags = {
    Environment = "production"
  }
}
```

### Canary Deployments (REST API)

#### Canary Deployment Configuration
```hcl
resource "aws_api_gateway_stage" "prod_canary" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "prod"

  # Canary settings
  canary_settings {
    percent_traffic          = 10  # 10% of traffic to canary
    deployment_id           = aws_api_gateway_deployment.canary.id
    stage_variable_overrides = {
      "lambdaAlias" = "canary"
    }
    use_stage_cache = false
  }

  # Stage variables for version control
  variables = {
    "lambdaAlias" = "live"
    "version"     = "v1.0.0"
  }

  tags = {
    Environment = "production"
    Deployment  = "canary"
  }
}

# Separate deployment for canary
resource "aws_api_gateway_deployment" "canary" {
  depends_on = [
    aws_api_gateway_method.users_get_v2,
    aws_api_gateway_integration.users_get_v2,
  ]

  rest_api_id = aws_api_gateway_rest_api.example.id

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_method.users_get_v2.id,
      aws_api_gateway_integration.users_get_v2.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Lambda alias for canary testing
resource "aws_lambda_alias" "canary" {
  name             = "canary"
  description      = "Canary alias for testing"
  function_name    = aws_lambda_function.users_handler.function_name
  function_version = aws_lambda_function.users_handler.version

  routing_config {
    additional_version_weights = {
      "${aws_lambda_function.users_handler_v2.version}" = 0.1
    }
  }
}
```

#### Canary Promotion Process
```hcl
# CloudWatch alarms for canary monitoring
resource "aws_cloudwatch_metric_alarm" "canary_errors" {
  alarm_name          = "api-gateway-canary-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "4XXError"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Sum"
  threshold           = "10"
  alarm_description   = "Canary deployment error rate too high"

  dimensions = {
    ApiName = aws_api_gateway_rest_api.example.name
    Stage   = aws_api_gateway_stage.prod_canary.stage_name
  }

  alarm_actions = [
    aws_sns_topic.canary_alerts.arn,
    aws_lambda_function.canary_rollback.arn
  ]
}

# Lambda function for automatic canary rollback
resource "aws_lambda_function" "canary_rollback" {
  filename         = "canary_rollback.zip"
  function_name    = "canary-rollback"
  role            = aws_iam_role.canary_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 60

  environment {
    variables = {
      API_ID    = aws_api_gateway_rest_api.example.id
      STAGE_NAME = aws_api_gateway_stage.prod_canary.stage_name
    }
  }
}
```

### Blue-Green Deployments

#### Blue-Green with Route53 Weighted Routing
```hcl
# Blue environment
resource "aws_api_gateway_rest_api" "blue" {
  name        = "example-api-blue"
  description = "Blue environment for API"

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = {
    Environment = "production"
    Color       = "blue"
  }
}

# Green environment
resource "aws_api_gateway_rest_api" "green" {
  name        = "example-api-green"
  description = "Green environment for API"

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = {
    Environment = "production"
    Color       = "green"
  }
}

# Blue domain
resource "aws_api_gateway_domain_name" "blue" {
  domain_name              = "api-blue.example.com"
  regional_certificate_arn = aws_acm_certificate_validation.api_cert.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

# Green domain
resource "aws_api_gateway_domain_name" "green" {
  domain_name              = "api-green.example.com"
  regional_certificate_arn = aws_acm_certificate_validation.api_cert.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

# Weighted routing for blue-green deployment
resource "aws_route53_record" "api_blue" {
  zone_id = var.hosted_zone_id
  name    = "api.example.com"
  type    = "A"

  set_identifier = "blue"

  weighted_routing_policy {
    weight = var.blue_weight  # 100 for full blue, 0 for full green
  }

  alias {
    name                   = aws_api_gateway_domain_name.blue.regional_domain_name
    zone_id                = aws_api_gateway_domain_name.blue.regional_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "api_green" {
  zone_id = var.hosted_zone_id
  name    = "api.example.com"
  type    = "A"

  set_identifier = "green"

  weighted_routing_policy {
    weight = var.green_weight  # 0 for full blue, 100 for full green
  }

  alias {
    name                   = aws_api_gateway_domain_name.green.regional_domain_name
    zone_id                = aws_api_gateway_domain_name.green.regional_zone_id
    evaluate_target_health = true
  }
}
```

## Monitoring & Logging

### CloudWatch Integration

#### CloudWatch Log Groups
```hcl
# Access logs for REST API
resource "aws_cloudwatch_log_group" "api_gateway_access" {
  name              = "/aws/apigateway/access-logs"
  retention_in_days = 30

  tags = {
    Environment = "production"
    Purpose     = "api-access-logs"
  }
}

# Execution logs for REST API
resource "aws_cloudwatch_log_group" "api_gateway_execution" {
  name              = "/aws/apigateway/execution-logs"
  retention_in_days = 7

  tags = {
    Environment = "production"
    Purpose     = "api-execution-logs"
  }
}

# HTTP API logs
resource "aws_cloudwatch_log_group" "http_api_logs" {
  name              = "/aws/apigatewayv2/http-api"
  retention_in_days = 14

  tags = {
    Environment = "production"
    Purpose     = "http-api-logs"
  }
}

# WebSocket API logs
resource "aws_cloudwatch_log_group" "websocket_api_logs" {
  name              = "/aws/apigatewayv2/websocket-api"
  retention_in_days = 14

  tags = {
    Environment = "production"
    Purpose     = "websocket-api-logs"
  }
}
```

#### REST API Logging Configuration
```hcl
# API Gateway account settings for CloudWatch
resource "aws_api_gateway_account" "main" {
  cloudwatch_role_arn = aws_iam_role.api_gateway_cloudwatch.arn
}

# IAM role for API Gateway CloudWatch access
resource "aws_iam_role" "api_gateway_cloudwatch" {
  name = "api-gateway-cloudwatch-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "api_gateway_cloudwatch" {
  role       = aws_iam_role.api_gateway_cloudwatch.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs"
}

# Stage with comprehensive logging
resource "aws_api_gateway_stage" "monitored" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "prod"

  # Access logging
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gateway_access.arn
    format = jsonencode({
      requestId        = "$context.requestId"
      extendedRequestId = "$context.extendedRequestId"
      ip               = "$context.identity.sourceIp"
      caller           = "$context.identity.caller"
      user             = "$context.identity.user"
      requestTime      = "$context.requestTime"
      httpMethod       = "$context.httpMethod"
      resourcePath     = "$context.resourcePath"
      status           = "$context.status"
      protocol         = "$context.protocol"
      responseLength   = "$context.responseLength"
      requestLength    = "$context.requestLength"
      responseTime     = "$context.responseTime"
      error            = "$context.error.message"
      integrationError = "$context.integrationErrorMessage"
      userAgent        = "$context.identity.userAgent"
      apiKey           = "$context.identity.apiKey"
    })
  }

  # X-Ray tracing
  xray_tracing_enabled = true

  # Method-specific logging settings
  settings {
    # Enable execution logging
    logging_level                            = "INFO"  # OFF, ERROR, INFO
    data_trace_enabled                      = true
    metrics_enabled                         = true

    # Method-specific settings
    "users/GET/logging/level"               = "INFO"
    "users/GET/logging/data_trace"          = true
    "users/GET/metrics/enabled"             = true
    "users/POST/logging/level"              = "ERROR"
    "users/POST/logging/data_trace"         = false
    "users/POST/metrics/enabled"            = true
  }

  tags = {
    Environment = "production"
  }
}
```

#### HTTP API Logging Configuration
```hcl
resource "aws_apigatewayv2_stage" "monitored_http" {
  api_id      = aws_apigatewayv2_api.example.id
  name        = "prod"
  auto_deploy = true

  # Access logging
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.http_api_logs.arn
    format = jsonencode({
      requestId        = "$context.requestId"
      ip               = "$context.identity.sourceIp"
      requestTime      = "$context.requestTime"
      httpMethod       = "$context.httpMethod"
      routeKey         = "$context.routeKey"
      status           = "$context.status"
      protocol         = "$context.protocol"
      responseLength   = "$context.responseLength"
      responseTime     = "$context.responseTime"
      error            = "$context.error.message"
      integrationError = "$context.integrationErrorMessage"
      integrationLatency = "$context.integrationLatency"
      responseLatency  = "$context.responseLatency"
      userAgent        = "$context.identity.userAgent"
      cognitoIdentityId = "$context.identity.cognitoIdentityId"
    })
  }

  # Route-specific settings
  route_settings {
    route_key                = "GET /users"
    detailed_metrics_enabled = true
    logging_level           = "INFO"
    data_trace_enabled      = true
    throttling_rate_limit   = 1000
    throttling_burst_limit  = 2000
  }

  route_settings {
    route_key                = "POST /users"
    detailed_metrics_enabled = true
    logging_level           = "ERROR"
    data_trace_enabled      = false
    throttling_rate_limit   = 500
    throttling_burst_limit  = 1000
  }

  tags = {
    Environment = "production"
  }
}
```

### X-Ray Tracing

#### X-Ray Configuration
```hcl
# REST API with X-Ray tracing
resource "aws_api_gateway_stage" "traced" {
  deployment_id = aws_api_gateway_deployment.example.id
  rest_api_id   = aws_api_gateway_rest_api.example.id
  stage_name    = "prod"

  xray_tracing_enabled = true

  tags = {
    Environment = "production"
    Tracing     = "enabled"
  }
}

# Lambda function with X-Ray tracing
resource "aws_lambda_function" "traced_function" {
  filename         = "function.zip"
  function_name    = "traced-function"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"

  tracing_config {
    mode = "Active"  # Active or PassThrough
  }

  environment {
    variables = {
      _X_AMZN_TRACE_ID = "Root=1-5e1b4151-5ac6c58b1a5a5a5a5a5a5a5a"
    }
  }
}

# IAM role with X-Ray permissions
resource "aws_iam_role_policy_attachment" "lambda_xray" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess"
}
```

### CloudWatch Metrics and Alarms

#### Comprehensive Monitoring Setup
```hcl
# API Gateway error rate alarm
resource "aws_cloudwatch_metric_alarm" "api_error_rate" {
  alarm_name          = "api-gateway-error-rate"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "4XXError"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Sum"
  threshold           = "50"
  alarm_description   = "API Gateway 4XX error rate is too high"
  treat_missing_data  = "notBreaching"

  dimensions = {
    ApiName = aws_api_gateway_rest_api.example.name
    Stage   = aws_api_gateway_stage.prod.stage_name
  }

  alarm_actions = [
    aws_sns_topic.alerts.arn,
    aws_autoscaling_policy.scale_up.arn
  ]

  ok_actions = [
    aws_sns_topic.alerts.arn
  ]

  tags = {
    Environment = "production"
    AlertType   = "error-rate"
  }
}

# API Gateway latency alarm
resource "aws_cloudwatch_metric_alarm" "api_latency" {
  alarm_name          = "api-gateway-latency"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "Latency"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Average"
  threshold           = "5000"  # 5 seconds
  alarm_description   = "API Gateway latency is too high"

  dimensions = {
    ApiName = aws_api_gateway_rest_api.example.name
    Stage   = aws_api_gateway_stage.prod.stage_name
  }

  alarm_actions = [aws_sns_topic.alerts.arn]
}

# Integration error alarm
resource "aws_cloudwatch_metric_alarm" "integration_errors" {
  alarm_name          = "api-gateway-integration-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "IntegrationLatency"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Average"
  threshold           = "10000"  # 10 seconds
  alarm_description   = "API Gateway integration latency is too high"

  dimensions = {
    ApiName = aws_api_gateway_rest_api.example.name
    Stage   = aws_api_gateway_stage.prod.stage_name
  }

  alarm_actions = [aws_sns_topic.alerts.arn]
}

# Cache hit rate alarm (for cached APIs)
resource "aws_cloudwatch_metric_alarm" "cache_hit_rate" {
  alarm_name          = "api-gateway-cache-hit-rate"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "CacheHitCount"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Sum"
  threshold           = "50"
  alarm_description   = "API Gateway cache hit rate is too low"

  dimensions = {
    ApiName = aws_api_gateway_rest_api.example.name
    Stage   = aws_api_gateway_stage.prod.stage_name
  }

  alarm_actions = [aws_sns_topic.alerts.arn]
}
```

#### Custom Metrics and Dashboards
```hcl
# SNS topic for alerts
resource "aws_sns_topic" "alerts" {
  name = "api-gateway-alerts"

  tags = {
    Environment = "production"
  }
}

resource "aws_sns_topic_subscription" "email_alerts" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = var.alert_email
}

# CloudWatch Dashboard
resource "aws_cloudwatch_dashboard" "api_gateway" {
  dashboard_name = "API-Gateway-Monitoring"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/ApiGateway", "Count", "ApiName", aws_api_gateway_rest_api.example.name, "Stage", aws_api_gateway_stage.prod.stage_name],
            [".", "4XXError", ".", ".", ".", "."],
            [".", "5XXError", ".", ".", ".", "."]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.region
          title   = "API Gateway Request Metrics"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/ApiGateway", "Latency", "ApiName", aws_api_gateway_rest_api.example.name, "Stage", aws_api_gateway_stage.prod.stage_name],
            [".", "IntegrationLatency", ".", ".", ".", "."]
          ]
          view   = "timeSeries"
          region = var.region
          title  = "API Gateway Latency"
          period = 300
          yAxis = {
            left = {
              min = 0
            }
          }
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 12
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/ApiGateway", "CacheHitCount", "ApiName", aws_api_gateway_rest_api.example.name, "Stage", aws_api_gateway_stage.prod.stage_name],
            [".", "CacheMissCount", ".", ".", ".", "."]
          ]
          view   = "timeSeries"
          region = var.region
          title  = "API Gateway Cache Performance"
          period = 300
        }
      }
    ]
  })
}

# Log insights queries
resource "aws_cloudwatch_query_definition" "api_errors" {
  name = "API Gateway Errors"

  log_group_names = [
    aws_cloudwatch_log_group.api_gateway_access.name
  ]

  query_string = <<EOF
fields @timestamp, requestId, status, error, httpMethod, resourcePath
| filter status >= 400
| sort @timestamp desc
| limit 100
EOF
}

resource "aws_cloudwatch_query_definition" "slow_requests" {
  name = "Slow API Requests"

  log_group_names = [
    aws_cloudwatch_log_group.api_gateway_access.name
  ]

  query_string = <<EOF
fields @timestamp, requestId, responseTime, httpMethod, resourcePath
| filter responseTime > 5000
| sort responseTime desc
| limit 50
EOF
}

# Composite alarm for overall API health
resource "aws_cloudwatch_composite_alarm" "api_health" {
  alarm_name        = "api-gateway-overall-health"
  alarm_description = "Overall API Gateway health status"

  alarm_rule = join(" OR ", [
    "ALARM(${aws_cloudwatch_metric_alarm.api_error_rate.alarm_name})",
    "ALARM(${aws_cloudwatch_metric_alarm.api_latency.alarm_name})",
    "ALARM(${aws_cloudwatch_metric_alarm.integration_errors.alarm_name})"
  ])

  alarm_actions = [aws_sns_topic.alerts.arn]
  ok_actions    = [aws_sns_topic.alerts.arn]

  tags = {
    Environment = "production"
    AlertType   = "composite"
  }
}
```

### Log Analysis and Insights

#### Advanced Log Analysis
```hcl
# CloudWatch Insights for API analysis
resource "aws_cloudwatch_query_definition" "top_endpoints" {
  name = "Top API Endpoints"

  log_group_names = [
    aws_cloudwatch_log_group.api_gateway_access.name
  ]

  query_string = <<EOF
fields httpMethod, resourcePath
| filter status = 200
| stats count() as requests by httpMethod, resourcePath
| sort requests desc
| limit 20
EOF
}

resource "aws_cloudwatch_query_definition" "user_agents" {
  name = "User Agents Analysis"

  log_group_names = [
    aws_cloudwatch_log_group.api_gateway_access.name
  ]

  query_string = <<EOF
fields userAgent
| stats count() as requests by userAgent
| sort requests desc
| limit 10
EOF
}

resource "aws_cloudwatch_query_definition" "geographic_distribution" {
  name = "Geographic Distribution"

  log_group_names = [
    aws_cloudwatch_log_group.api_gateway_access.name
  ]

  query_string = <<EOF
fields ip
| stats count() as requests by ip
| sort requests desc
| limit 50
EOF
}

# Custom metric filters
resource "aws_cloudwatch_log_metric_filter" "api_errors_by_type" {
  name           = "api-errors-by-type"
  log_group_name = aws_cloudwatch_log_group.api_gateway_access.name
  pattern        = "[timestamp, requestId, ip, caller, user, requestTime, httpMethod, resourcePath, status=4*, protocol, responseLength]"

  metric_transformation {
    name      = "APIErrors4XX"
    namespace = "Custom/APIGateway"
    value     = "1"

    default_value = 0
  }
}

resource "aws_cloudwatch_log_metric_filter" "slow_requests_filter" {
  name           = "slow-requests"
  log_group_name = aws_cloudwatch_log_group.api_gateway_access.name
  pattern        = "[timestamp, requestId, ip, caller, user, requestTime, httpMethod, resourcePath, status, protocol, responseLength, requestLength, responseTime>5000]"

  metric_transformation {
    name      = "SlowRequests"
    namespace = "Custom/APIGateway"
    value     = "1"
  }
}
```

### Performance Monitoring

#### Lambda Integration Monitoring
```hcl
# Lambda function monitoring
resource "aws_cloudwatch_metric_alarm" "lambda_errors" {
  alarm_name          = "lambda-function-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Sum"
  threshold           = "5"
  alarm_description   = "Lambda function error rate is too high"

  dimensions = {
    FunctionName = aws_lambda_function.users_handler.function_name
  }

  alarm_actions = [aws_sns_topic.alerts.arn]
}

resource "aws_cloudwatch_metric_alarm" "lambda_duration" {
  alarm_name          = "lambda-function-duration"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "Duration"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Average"
  threshold           = "10000"  # 10 seconds
  alarm_description   = "Lambda function duration is too high"

  dimensions = {
    FunctionName = aws_lambda_function.users_handler.function_name
  }

  alarm_actions = [aws_sns_topic.alerts.arn]
}

# DynamoDB monitoring (for direct integrations)
resource "aws_cloudwatch_metric_alarm" "dynamodb_throttles" {
  alarm_name          = "dynamodb-throttles"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "ThrottledRequests"
  namespace           = "AWS/DynamoDB"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "DynamoDB requests are being throttled"

  dimensions = {
    TableName = aws_dynamodb_table.users.name
  }

  alarm_actions = [aws_sns_topic.alerts.arn]
}
```

## Production Examples

### Complete REST API with Lambda Backend

#### Full-Stack REST API Example
```hcl
# Variables
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "api_name" {
  description = "API name"
  type        = string
  default     = "user-management-api"
}

variable "domain_name" {
  description = "Custom domain name"
  type        = string
  default     = "api.example.com"
}

variable "hosted_zone_id" {
  description = "Route53 hosted zone ID"
  type        = string
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# DynamoDB table
resource "aws_dynamodb_table" "users" {
  name           = "${var.api_name}-users"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "userId"

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }

  global_secondary_index {
    name     = "EmailIndex"
    hash_key = "email"
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# Lambda execution role
resource "aws_iam_role" "lambda_role" {
  name = "${var.api_name}-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_policy" {
  name = "${var.api_name}-lambda-policy"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = [
          aws_dynamodb_table.users.arn,
          "${aws_dynamodb_table.users.arn}/index/*"
        ]
      }
    ]
  })
}

# Lambda functions
resource "aws_lambda_function" "users_handler" {
  filename         = "users_handler.zip"
  function_name    = "${var.api_name}-users-handler"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      TABLE_NAME = aws_dynamodb_table.users.name
      REGION     = data.aws_region.current.name
    }
  }

  tracing_config {
    mode = "Active"
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# API Gateway REST API
resource "aws_api_gateway_rest_api" "main" {
  name        = var.api_name
  description = "User Management API"

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# API Gateway resources
resource "aws_api_gateway_resource" "users" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "users"
}

resource "aws_api_gateway_resource" "user_id" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users.id
  path_part   = "{userId}"
}

# Request validator
resource "aws_api_gateway_request_validator" "validator" {
  name                        = "${var.api_name}-validator"
  rest_api_id                = aws_api_gateway_rest_api.main.id
  validate_request_body      = true
  validate_request_parameters = true
}

# Models
resource "aws_api_gateway_model" "user" {
  rest_api_id  = aws_api_gateway_rest_api.main.id
  name         = "User"
  content_type = "application/json"

  schema = jsonencode({
    "$schema": "http://json-schema.org/draft-04/schema#",
    "title": "User Schema",
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "minLength": 1,
        "maxLength": 100
      },
      "email": {
        "type": "string",
        "format": "email"
      },
      "age": {
        "type": "integer",
        "minimum": 18,
        "maximum": 120
      }
    },
    "required": ["name", "email"],
    "additionalProperties": false
  })
}

# Methods and integrations
resource "aws_api_gateway_method" "users_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "GET"
  authorization = "NONE"

  request_parameters = {
    "method.request.querystring.limit"  = false
    "method.request.querystring.offset" = false
  }
}

resource "aws_api_gateway_integration" "users_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.users_handler.invoke_arn
}

resource "aws_api_gateway_method" "users_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "POST"
  authorization = "NONE"

  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_models = {
    "application/json" = aws_api_gateway_model.user.name
  }
}

resource "aws_api_gateway_integration" "users_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.users_handler.invoke_arn
}

resource "aws_api_gateway_method" "user_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.user_id.id
  http_method   = "GET"
  authorization = "NONE"

  request_parameters = {
    "method.request.path.userId" = true
  }
}

resource "aws_api_gateway_integration" "user_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.user_id.id
  http_method = aws_api_gateway_method.user_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.users_handler.invoke_arn
}

# CORS configuration
module "cors" {
  source = "./modules/cors"

  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users.id

  allow_headers = ["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key"]
  allow_methods = ["GET", "POST", "OPTIONS"]
  allow_origin  = "*"
}

# Lambda permissions
resource "aws_lambda_permission" "api_gateway" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.users_handler.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/*/*"
}

# Deployment
resource "aws_api_gateway_deployment" "main" {
  depends_on = [
    aws_api_gateway_method.users_get,
    aws_api_gateway_method.users_post,
    aws_api_gateway_method.user_get,
    aws_api_gateway_integration.users_get,
    aws_api_gateway_integration.users_post,
    aws_api_gateway_integration.user_get,
  ]

  rest_api_id = aws_api_gateway_rest_api.main.id

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.users.id,
      aws_api_gateway_resource.user_id.id,
      aws_api_gateway_method.users_get.id,
      aws_api_gateway_method.users_post.id,
      aws_api_gateway_method.user_get.id,
      aws_api_gateway_integration.users_get.id,
      aws_api_gateway_integration.users_post.id,
      aws_api_gateway_integration.user_get.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

# CloudWatch Log Groups
resource "aws_cloudwatch_log_group" "api_gateway" {
  name              = "/aws/apigateway/${var.api_name}"
  retention_in_days = 14

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# Production stage
resource "aws_api_gateway_stage" "prod" {
  deployment_id = aws_api_gateway_deployment.main.id
  rest_api_id   = aws_api_gateway_rest_api.main.id
  stage_name    = "prod"

  # Caching
  cache_cluster_enabled = true
  cache_cluster_size    = "0.5"

  # Throttling
  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  # Logging
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gateway.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      requestTime    = "$context.requestTime"
      httpMethod     = "$context.httpMethod"
      resourcePath   = "$context.resourcePath"
      status         = "$context.status"
      responseLength = "$context.responseLength"
      responseTime   = "$context.responseTime"
    })
  }

  xray_tracing_enabled = true

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}
```

### Complete HTTP API with JWT Authentication

#### Production HTTP API Example
```hcl
# Cognito User Pool for JWT authentication
resource "aws_cognito_user_pool" "main" {
  name = "${var.api_name}-user-pool"

  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_symbols   = true
    require_uppercase = true
  }

  username_attributes = ["email"]

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

resource "aws_cognito_user_pool_client" "main" {
  name         = "${var.api_name}-client"
  user_pool_id = aws_cognito_user_pool.main.id

  allowed_oauth_flows                  = ["code", "implicit"]
  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_scopes                = ["email", "openid", "profile"]
  callback_urls                       = ["https://app.example.com/callback"]

  access_token_validity  = 60
  id_token_validity     = 60
  refresh_token_validity = 30

  prevent_user_existence_errors = "ENABLED"
}

# HTTP API
resource "aws_apigatewayv2_api" "main" {
  name          = "${var.api_name}-http"
  protocol_type = "HTTP"
  description   = "HTTP API with JWT authentication"

  cors_configuration {
    allow_credentials = true
    allow_headers = [
      "content-type",
      "x-amz-date",
      "authorization",
      "x-api-key",
      "x-amz-security-token"
    ]
    allow_methods = ["*"]
    allow_origins = ["https://app.example.com"]
    max_age      = 86400
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# JWT Authorizer
resource "aws_apigatewayv2_authorizer" "jwt" {
  api_id           = aws_apigatewayv2_api.main.id
  authorizer_type  = "JWT"
  identity_sources = ["$request.header.Authorization"]
  name             = "jwt-authorizer"

  jwt_configuration {
    audience = [aws_cognito_user_pool_client.main.id]
    issuer   = "https://cognito-idp.${data.aws_region.current.name}.amazonaws.com/${aws_cognito_user_pool.main.id}"
  }
}

# Lambda integration
resource "aws_apigatewayv2_integration" "lambda" {
  api_id           = aws_apigatewayv2_api.main.id
  integration_type = "AWS_PROXY"

  connection_type      = "INTERNET"
  description         = "Lambda integration"
  integration_method  = "POST"
  integration_uri     = aws_lambda_function.users_handler.invoke_arn
  payload_format_version = "2.0"
}

# Routes
resource "aws_apigatewayv2_route" "get_users" {
  api_id    = aws_apigatewayv2_api.main.id
  route_key = "GET /users"

  authorization_type = "JWT"
  authorizer_id      = aws_apigatewayv2_authorizer.jwt.id
  target            = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}

resource "aws_apigatewayv2_route" "post_users" {
  api_id    = aws_apigatewayv2_api.main.id
  route_key = "POST /users"

  authorization_type = "JWT"
  authorizer_id      = aws_apigatewayv2_authorizer.jwt.id
  target            = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}

resource "aws_apigatewayv2_route" "get_user" {
  api_id    = aws_apigatewayv2_api.main.id
  route_key = "GET /users/{userId}"

  authorization_type = "JWT"
  authorizer_id      = aws_apigatewayv2_authorizer.jwt.id
  target            = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}

# Public health check route
resource "aws_apigatewayv2_route" "health" {
  api_id    = aws_apigatewayv2_api.main.id
  route_key = "GET /health"

  authorization_type = "NONE"
  target            = "integrations/${aws_apigatewayv2_integration.lambda.id}"
}

# Stage
resource "aws_apigatewayv2_stage" "prod" {
  api_id      = aws_apigatewayv2_api.main.id
  name        = "prod"
  auto_deploy = true

  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.http_api.arn
    format = jsonencode({
      requestId        = "$context.requestId"
      ip               = "$context.identity.sourceIp"
      requestTime      = "$context.requestTime"
      httpMethod       = "$context.httpMethod"
      routeKey         = "$context.routeKey"
      status           = "$context.status"
      responseLength   = "$context.responseLength"
      responseTime     = "$context.responseTime"
      integrationLatency = "$context.integrationLatency"
    })
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

resource "aws_cloudwatch_log_group" "http_api" {
  name              = "/aws/apigatewayv2/${var.api_name}-http"
  retention_in_days = 14

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# Lambda permission for HTTP API
resource "aws_lambda_permission" "http_api" {
  statement_id  = "AllowExecutionFromHTTPAPI"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.users_handler.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.main.execution_arn}/*/*"
}
```

### Complete WebSocket API for Real-time Chat

#### Production WebSocket API Example
```hcl
# WebSocket API
resource "aws_apigatewayv2_api" "websocket" {
  name                       = "${var.api_name}-websocket"
  protocol_type             = "WEBSOCKET"
  route_selection_expression = "$request.body.action"
  description               = "WebSocket API for real-time chat"

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# DynamoDB table for connections
resource "aws_dynamodb_table" "connections" {
  name           = "${var.api_name}-connections"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "connectionId"

  attribute {
    name = "connectionId"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "roomId"
    type = "S"
  }

  global_secondary_index {
    name     = "UserIndex"
    hash_key = "userId"
  }

  global_secondary_index {
    name     = "RoomIndex"
    hash_key = "roomId"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# Lambda functions for WebSocket
resource "aws_lambda_function" "websocket_connect" {
  filename         = "websocket_connect.zip"
  function_name    = "${var.api_name}-websocket-connect"
  role            = aws_iam_role.websocket_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      CONNECTIONS_TABLE = aws_dynamodb_table.connections.name
      REGION           = data.aws_region.current.name
    }
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

resource "aws_lambda_function" "websocket_disconnect" {
  filename         = "websocket_disconnect.zip"
  function_name    = "${var.api_name}-websocket-disconnect"
  role            = aws_iam_role.websocket_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      CONNECTIONS_TABLE = aws_dynamodb_table.connections.name
      REGION           = data.aws_region.current.name
    }
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

resource "aws_lambda_function" "websocket_message" {
  filename         = "websocket_message.zip"
  function_name    = "${var.api_name}-websocket-message"
  role            = aws_iam_role.websocket_lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      CONNECTIONS_TABLE = aws_dynamodb_table.connections.name
      API_GATEWAY_ENDPOINT = aws_apigatewayv2_stage.websocket_prod.invoke_url
      REGION           = data.aws_region.current.name
    }
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# IAM role for WebSocket Lambda functions
resource "aws_iam_role" "websocket_lambda_role" {
  name = "${var.api_name}-websocket-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "websocket_lambda_policy" {
  name = "${var.api_name}-websocket-lambda-policy"
  role = aws_iam_role.websocket_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:ManageConnections"
        ]
        Resource = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = [
          aws_dynamodb_table.connections.arn,
          "${aws_dynamodb_table.connections.arn}/index/*"
        ]
      }
    ]
  })
}

# WebSocket integrations
resource "aws_apigatewayv2_integration" "websocket_connect" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Connect integration"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_connect.invoke_arn
}

resource "aws_apigatewayv2_integration" "websocket_disconnect" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Disconnect integration"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_disconnect.invoke_arn
}

resource "aws_apigatewayv2_integration" "websocket_message" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"

  connection_type    = "INTERNET"
  description       = "Message integration"
  integration_method = "POST"
  integration_uri   = aws_lambda_function.websocket_message.invoke_arn
}

# WebSocket routes
resource "aws_apigatewayv2_route" "websocket_connect" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$connect"
  target    = "integrations/${aws_apigatewayv2_integration.websocket_connect.id}"
}

resource "aws_apigatewayv2_route" "websocket_disconnect" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$disconnect"
  target    = "integrations/${aws_apigatewayv2_integration.websocket_disconnect.id}"
}

resource "aws_apigatewayv2_route" "websocket_default" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.websocket_message.id}"
}

resource "aws_apigatewayv2_route" "send_message" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "sendMessage"
  target    = "integrations/${aws_apigatewayv2_integration.websocket_message.id}"
}

# WebSocket stage
resource "aws_apigatewayv2_stage" "websocket_prod" {
  api_id      = aws_apigatewayv2_api.websocket.id
  name        = "prod"
  auto_deploy = true

  throttle_settings {
    rate_limit  = 1000
    burst_limit = 2000
  }

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.websocket_api.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      connectionId   = "$context.connectionId"
      routeKey       = "$context.routeKey"
      eventType      = "$context.eventType"
      status         = "$context.status"
      error          = "$context.error.message"
      requestTime    = "$context.requestTime"
      responseLength = "$context.responseLength"
    })
  }

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

resource "aws_cloudwatch_log_group" "websocket_api" {
  name              = "/aws/apigatewayv2/${var.api_name}-websocket"
  retention_in_days = 14

  tags = {
    Environment = var.environment
    Application = var.api_name
  }
}

# Lambda permissions for WebSocket API
resource "aws_lambda_permission" "websocket_connect" {
  statement_id  = "AllowExecutionFromWebSocketAPI"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.websocket_connect.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
}

resource "aws_lambda_permission" "websocket_disconnect" {
  statement_id  = "AllowExecutionFromWebSocketAPI"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.websocket_disconnect.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
}

resource "aws_lambda_permission" "websocket_message" {
  statement_id  = "AllowExecutionFromWebSocketAPI"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.websocket_message.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
}
```

### Outputs and Summary

#### Terraform Outputs
```hcl
# Outputs
output "rest_api_url" {
  description = "REST API URL"
  value       = "https://${aws_api_gateway_rest_api.main.id}.execute-api.${data.aws_region.current.name}.amazonaws.com/${aws_api_gateway_stage.prod.stage_name}"
}

output "http_api_url" {
  description = "HTTP API URL"
  value       = aws_apigatewayv2_stage.prod.invoke_url
}

output "websocket_api_url" {
  description = "WebSocket API URL"
  value       = aws_apigatewayv2_stage.websocket_prod.invoke_url
}

output "custom_domain_url" {
  description = "Custom domain URL"
  value       = "https://${var.domain_name}"
}

output "cognito_user_pool_id" {
  description = "Cognito User Pool ID"
  value       = aws_cognito_user_pool.main.id
}

output "cognito_client_id" {
  description = "Cognito Client ID"
  value       = aws_cognito_user_pool_client.main.id
}
```

## Summary

This comprehensive guide covers all aspects of AWS API Gateway with Terraform, from basic concepts to production-ready implementations. Key takeaways:

### Best Practices Summary
1. **Choose the right API type** based on your requirements
2. **Use proper dependency management** in Terraform deployments
3. **Implement comprehensive monitoring** and logging
4. **Follow security best practices** with proper authentication
5. **Use infrastructure as code** for consistent deployments
6. **Plan for scalability** with appropriate throttling and caching
7. **Monitor costs** and optimize resource usage

### Common Patterns
- **REST APIs** for full-featured, enterprise applications
- **HTTP APIs** for cost-effective, high-performance APIs
- **WebSocket APIs** for real-time, bidirectional communication
- **Lambda proxy integrations** for serverless architectures
- **Direct AWS service integrations** for specific use cases

### Production Considerations
- Always use custom domain names for production
- Implement proper error handling and monitoring
- Use canary or blue-green deployments for zero-downtime updates
- Configure appropriate caching and throttling
- Implement comprehensive security measures
- Plan for disaster recovery and backup strategies

This guide provides the foundation for mastering AWS API Gateway with Terraform at an expert level.
```
